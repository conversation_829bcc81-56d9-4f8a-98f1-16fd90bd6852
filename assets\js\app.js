/**
 * SallamChat - Main Application JavaScript
 * Handles theme switching, UI interactions, and general app functionality
 */

// Global variables
let currentTheme = 'light';
let currentUser = null;

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    console.log('SallamChat App Initialized');

    // Wait for database services to be ready
    waitForServices().then(() => {
        // Initialize theme
        initializeTheme();

        // Initialize common UI elements
        initializeUI();

        // Set current year in footer
        setCurrentYear();

        // Initialize page-specific functionality
        const currentPage = getCurrentPage();
        initializePage(currentPage);
    }).catch(error => {
        console.error('Failed to initialize services:', error);
        // Continue with basic initialization
        initializeTheme();
        initializeUI();
        setCurrentYear();
    });
});

/**
 * Wait for database services to be ready
 */
function waitForServices() {
    return new Promise((resolve) => {
        let attempts = 0;
        const maxAttempts = 50; // 5 seconds max wait

        const checkServices = () => {
            attempts++;

            // Check if any database service is available
            if (window.firebaseAuth || window.sallamChatDB?.isReady || window.mockFirebase) {
                console.log('Database services ready');
                resolve();
                return;
            }

            if (attempts >= maxAttempts) {
                console.warn('Database services not ready, continuing anyway');
                resolve();
                return;
            }

            setTimeout(checkServices, 100);
        };

        checkServices();
    });
}

/**
 * Initialize theme system
 */
function initializeTheme() {
    // Get saved theme from localStorage
    const savedTheme = localStorage.getItem('sallamchat_theme') || 'light';
    currentTheme = savedTheme;

    // Apply theme
    applyTheme(currentTheme);

    // Setup theme toggle button
    const themeToggle = document.getElementById('themeToggle');
    const themeIcon = document.getElementById('themeIcon');

    if (themeToggle && themeIcon) {
        // Update icon based on current theme
        updateThemeIcon(themeIcon, currentTheme);

        // Add click event listener
        themeToggle.addEventListener('click', function() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(currentTheme);
            updateThemeIcon(themeIcon, currentTheme);
            localStorage.setItem('sallamchat_theme', currentTheme);

            // Add animation effect
            themeToggle.style.transform = 'scale(0.9)';
            setTimeout(() => {
                themeToggle.style.transform = 'scale(1)';
            }, 150);
        });
    }
}

/**
 * Apply theme to document
 */
function applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
}

/**
 * Update theme toggle icon
 */
function updateThemeIcon(iconElement, theme) {
    if (theme === 'dark') {
        iconElement.className = 'bi bi-sun-fill';
    } else {
        iconElement.className = 'bi bi-moon-fill';
    }
}

/**
 * Initialize common UI elements
 */
function initializeUI() {
    // Initialize password toggle buttons
    initializePasswordToggles();

    // Initialize loading spinner
    initializeLoadingSpinner();

    // Initialize alert system
    initializeAlertSystem();

    // Initialize tooltips and popovers if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
}

/**
 * Initialize password toggle functionality
 */
function initializePasswordToggles() {
    const toggleButtons = document.querySelectorAll('#togglePassword, #toggleConfirmPassword');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.id === 'togglePassword' ? 'password' : 'confirmPassword';
            const passwordInput = document.getElementById(targetId);
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
    });
}

/**
 * Initialize loading spinner
 */
function initializeLoadingSpinner() {
    window.showLoading = function() {
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) {
            spinner.classList.add('show');
        }
    };

    window.hideLoading = function() {
        const spinner = document.getElementById('loadingSpinner');
        if (spinner) {
            spinner.classList.remove('show');
        }
    };
}

/**
 * Initialize alert system
 */
function initializeAlertSystem() {
    window.showAlert = function(message, type = 'info', duration = 5000) {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;

        const alertId = 'alert_' + Date.now();
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert" id="${alertId}">
                <i class="bi bi-${getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        alertContainer.insertAdjacentHTML('beforeend', alertHTML);

        // Auto-dismiss after duration
        if (duration > 0) {
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, duration);
        }
    };

    function getAlertIcon(type) {
        const icons = {
            'success': 'check-circle-fill',
            'danger': 'exclamation-triangle-fill',
            'warning': 'exclamation-triangle-fill',
            'info': 'info-circle-fill',
            'primary': 'info-circle-fill'
        };
        return icons[type] || 'info-circle-fill';
    }
}

/**
 * Set current year in footer
 */
function setCurrentYear() {
    const yearElements = document.querySelectorAll('#currentYear');
    const currentYear = new Date().getFullYear();

    yearElements.forEach(element => {
        element.textContent = currentYear;
    });
}

/**
 * Get current page name
 */
function getCurrentPage() {
    const path = window.location.pathname;
    const page = path.split('/').pop() || 'index.html';
    return page.replace('.html', '');
}

/**
 * Initialize page-specific functionality
 */
function initializePage(page) {
    switch (page) {
        case 'index':
        case '':
            initializeLoginPage();
            break;
        case 'register':
            initializeRegisterPage();
            break;
        case 'chat':
            initializeChatPage();
            break;
        case 'admin':
            initializeAdminPage();
            break;
        default:
            console.log('Unknown page:', page);
    }
}

/**
 * Initialize login page
 */
function initializeLoginPage() {
    console.log('Initializing login page');

    // Add fade-in animation
    document.body.classList.add('fade-in');

    // Focus on email input
    const emailInput = document.getElementById('email');
    if (emailInput) {
        setTimeout(() => emailInput.focus(), 500);
    }
}

/**
 * Initialize register page
 */
function initializeRegisterPage() {
    console.log('Initializing register page');

    // Add fade-in animation
    document.body.classList.add('fade-in');

    // Initialize password strength indicator
    initializePasswordStrength();

    // Focus on full name input
    const nameInput = document.getElementById('fullName');
    if (nameInput) {
        setTimeout(() => nameInput.focus(), 500);
    }
}

/**
 * Initialize password strength indicator
 */
function initializePasswordStrength() {
    const passwordInput = document.getElementById('password');
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');

    if (passwordInput && strengthFill && strengthText) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);

            // Update strength bar
            strengthFill.className = `strength-fill ${strength.class}`;
            strengthText.textContent = strength.text;
        });
    }
}

/**
 * Calculate password strength
 */
function calculatePasswordStrength(password) {
    let score = 0;

    if (password.length >= 6) score++;
    if (password.length >= 10) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    if (score < 2) {
        return { class: 'weak', text: 'ضعيف' };
    } else if (score < 4) {
        return { class: 'fair', text: 'متوسط' };
    } else if (score < 6) {
        return { class: 'good', text: 'جيد' };
    } else {
        return { class: 'strong', text: 'قوي' };
    }
}

/**
 * Initialize chat page
 */
function initializeChatPage() {
    console.log('Initializing chat page');

    // Check if user is authenticated
    checkAuthentication();
}

/**
 * Initialize admin page
 */
function initializeAdminPage() {
    console.log('Initializing admin page');
}

/**
 * Check user authentication
 */
function checkAuthentication() {
    console.log('🔍 checkAuthentication called for page:', getCurrentPage());

    // First check for saved user session
    const savedUser = localStorage.getItem('sallamchat_current_user');
    if (savedUser) {
        try {
            const userData = JSON.parse(savedUser);
            console.log('✅ Found saved user session:', userData.email);
            currentUser = userData;

            // If on login page and have valid session, redirect to chat
            if (getCurrentPage() === 'index' || getCurrentPage() === '') {
                console.log('🔄 Redirecting to chat (saved session)');
                window.location.href = 'chat.html';
                return;
            }

            // If on chat page, stay there
            if (getCurrentPage() === 'chat') {
                console.log('✅ Staying on chat page (saved session)');
                return;
            }

        } catch (error) {
            console.error('❌ Error parsing saved user session:', error);
            localStorage.removeItem('sallamchat_current_user');
        }
    }

    // Then check Firebase Auth if available
    if (window.firebaseAuth) {
        window.firebaseAuth.onAuthStateChanged(function(user) {
            console.log('🔄 Auth state changed:', user ? user.email : 'signed out');

            // Don't override if we already have a saved session
            if (!savedUser) {
                currentUser = user;
            }

            if (user) {
                console.log('User is signed in:', user.email);
                // User is signed in
                if (getCurrentPage() === 'index' || getCurrentPage() === '') {
                    // Redirect to chat if on login page
                    console.log('🔄 Redirecting to chat (Firebase auth)');
                    window.location.href = 'chat.html';
                }
            } else {
                console.log('User is signed out');
                // Only redirect if no saved session exists
                if (!savedUser && getCurrentPage() === 'chat') {
                    console.log('🔄 Redirecting to login (no auth)');
                    window.location.href = 'index.html';
                }
            }
        });
    } else {
        console.log('⚠️ Firebase Auth not available');

        // If no Firebase Auth and no saved session, redirect to login from chat page
        if (!savedUser && getCurrentPage() === 'chat') {
            console.log('🔄 Redirecting to login (no auth system)');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }
    }
}

/**
 * Utility function to format date
 */
function formatDate(date) {
    if (!date) return '';

    const now = new Date();
    const messageDate = new Date(date);
    const diffTime = Math.abs(now - messageDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
        return 'اليوم ' + messageDate.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    } else if (diffDays === 2) {
        return 'أمس ' + messageDate.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    } else if (diffDays <= 7) {
        return messageDate.toLocaleDateString('ar-SA', {
            weekday: 'long',
            hour: '2-digit',
            minute: '2-digit'
        });
    } else {
        return messageDate.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

/**
 * Utility function to generate unique ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

/**
 * Utility function to sanitize HTML
 */
function sanitizeHTML(str) {
    const temp = document.createElement('div');
    temp.textContent = str;
    return temp.innerHTML;
}

/**
 * Play notification sound
 */
function playNotificationSound(type = 'message') {
    try {
        const audio = new Audio(`assets/sounds/${type}-sound.mp3`);
        audio.volume = 0.5;
        audio.play().catch(e => console.log('Could not play sound:', e));
    } catch (error) {
        console.log('Sound file not found:', error);
    }
}

// Export functions for global use
window.SallamChat = {
    showAlert: window.showAlert,
    showLoading: window.showLoading,
    hideLoading: window.hideLoading,
    formatDate: formatDate,
    generateId: generateId,
    sanitizeHTML: sanitizeHTML,
    playNotificationSound: playNotificationSound,
    currentUser: () => currentUser,
    currentTheme: () => currentTheme
};

console.log('SallamChat App JavaScript loaded successfully');
