/**
 * SallamChat - Email Service
 * This service handles email sending to all email domains (Gmail, Hotmail, Outlook, Yahoo, etc.)
 */

/**
 * Email Service Class
 */
class EmailService {
    constructor() {
        this.isInitialized = false;
        this.config = null;
    }

    /**
     * Initialize email service with configuration
     */
    async initialize(config) {
        try {
            this.config = this.validateAndNormalizeConfig(config);
            this.isInitialized = true;
            console.log('Email service initialized successfully');
            return true;
        } catch (error) {
            console.error('Email service initialization failed:', error);
            throw error;
        }
    }

    /**
     * Validate and normalize email configuration
     */
    validateAndNormalizeConfig(config) {
        if (!config.senderEmail || !this.isValidEmail(config.senderEmail)) {
            throw new Error('Invalid sender email address');
        }

        if (!config.username || !config.password) {
            throw new Error('Username and password are required');
        }

        // Set SMTP configuration based on service
        const smtpConfig = this.getSMTPConfig(config.service, config);

        return {
            service: config.service,
            senderEmail: config.senderEmail,
            senderName: config.senderName || 'SallamChat Admin',
            smtp: smtpConfig,
            auth: {
                username: config.username,
                password: config.password
            }
        };
    }

    /**
     * Get SMTP configuration for different email services
     */
    getSMTPConfig(service, config) {
        const presets = {
            gmail: {
                host: 'smtp.gmail.com',
                port: 587,
                secure: false, // true for 465, false for other ports
                requireTLS: true
            },
            outlook: {
                host: 'smtp-mail.outlook.com',
                port: 587,
                secure: false,
                requireTLS: true
            },
            hotmail: {
                host: 'smtp-mail.outlook.com', // Hotmail uses Outlook SMTP
                port: 587,
                secure: false,
                requireTLS: true
            },
            yahoo: {
                host: 'smtp.mail.yahoo.com',
                port: 587,
                secure: false,
                requireTLS: true
            },
            smtp: {
                host: config.smtpHost,
                port: config.smtpPort || 587,
                secure: config.smtpSecurity === 'ssl',
                requireTLS: config.smtpSecurity === 'tls'
            }
        };

        return presets[service] || presets.smtp;
    }

    /**
     * Test email connection
     */
    async testConnection() {
        if (!this.isInitialized) {
            throw new Error('Email service not initialized');
        }

        try {
            // In a real implementation, this would test the SMTP connection
            // For now, we'll simulate the test
            console.log('Testing email connection...');
            
            // Simulate connection test
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Validate configuration
            if (!this.config.smtp.host) {
                throw new Error('SMTP host not configured');
            }

            console.log('Email connection test successful');
            return true;
        } catch (error) {
            console.error('Email connection test failed:', error);
            throw error;
        }
    }

    /**
     * Send email to single recipient
     */
    async sendEmail(to, subject, content, isHtml = true) {
        if (!this.isInitialized) {
            throw new Error('Email service not initialized');
        }

        if (!this.isValidEmail(to)) {
            throw new Error(`Invalid recipient email: ${to}`);
        }

        try {
            const emailData = {
                from: {
                    email: this.config.senderEmail,
                    name: this.config.senderName
                },
                to: to,
                subject: subject,
                content: content,
                isHtml: isHtml,
                timestamp: new Date().toISOString()
            };

            // In a real implementation, this would use a proper SMTP library
            // For demonstration, we'll simulate the email sending
            await this.simulateEmailSend(emailData);

            console.log(`Email sent successfully to ${to}`);
            return {
                success: true,
                messageId: this.generateMessageId(),
                recipient: to,
                timestamp: emailData.timestamp
            };

        } catch (error) {
            console.error(`Failed to send email to ${to}:`, error);
            throw error;
        }
    }

    /**
     * Send emails to multiple recipients
     */
    async sendBulkEmails(recipients, subject, content, isHtml = true, progressCallback = null) {
        if (!this.isInitialized) {
            throw new Error('Email service not initialized');
        }

        const results = {
            total: recipients.length,
            sent: 0,
            failed: 0,
            errors: []
        };

        for (let i = 0; i < recipients.length; i++) {
            const recipient = recipients[i];
            
            try {
                // Update progress
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: recipients.length,
                        recipient: recipient,
                        status: 'sending'
                    });
                }

                await this.sendEmail(recipient, subject, content, isHtml);
                results.sent++;

                // Update progress
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: recipients.length,
                        recipient: recipient,
                        status: 'success'
                    });
                }

            } catch (error) {
                results.failed++;
                results.errors.push({
                    recipient: recipient,
                    error: error.message
                });

                // Update progress
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: recipients.length,
                        recipient: recipient,
                        status: 'failed',
                        error: error.message
                    });
                }
            }

            // Small delay between emails to avoid rate limiting
            if (i < recipients.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        return results;
    }

    /**
     * Simulate email sending (replace with real SMTP implementation)
     */
    async simulateEmailSend(emailData) {
        // Simulate network delay
        const delay = Math.random() * 1000 + 500;
        await new Promise(resolve => setTimeout(resolve, delay));

        // Simulate occasional failures (10% failure rate for demo)
        if (Math.random() < 0.1) {
            const errors = [
                'SMTP connection timeout',
                'Authentication failed',
                'Recipient mailbox full',
                'Message rejected by server',
                'Rate limit exceeded'
            ];
            throw new Error(errors[Math.floor(Math.random() * errors.length)]);
        }

        // Log the email (in real implementation, this would be sent via SMTP)
        console.log('Email sent:', {
            to: emailData.to,
            subject: emailData.subject,
            from: emailData.from.email,
            timestamp: emailData.timestamp
        });
    }

    /**
     * Validate email address
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Generate unique message ID
     */
    generateMessageId() {
        return `${Date.now()}-${Math.random().toString(36).substring(2)}@sallamchat.local`;
    }

    /**
     * Get supported email domains
     */
    getSupportedDomains() {
        return [
            'gmail.com',
            'googlemail.com',
            'outlook.com',
            'hotmail.com',
            'live.com',
            'msn.com',
            'yahoo.com',
            'yahoo.co.uk',
            'yahoo.fr',
            'yahoo.de',
            'aol.com',
            'icloud.com',
            'me.com',
            'mac.com',
            'protonmail.com',
            'tutanota.com',
            'zoho.com',
            'mail.com',
            'gmx.com',
            'yandex.com',
            'mail.ru'
            // Add more domains as needed
        ];
    }

    /**
     * Check if email domain is supported
     */
    isDomainSupported(email) {
        const domain = email.split('@')[1]?.toLowerCase();
        return this.getSupportedDomains().includes(domain) || true; // Allow all domains
    }

    /**
     * Get email provider from email address
     */
    getEmailProvider(email) {
        const domain = email.split('@')[1]?.toLowerCase();
        
        if (domain?.includes('gmail') || domain?.includes('googlemail')) {
            return 'Gmail';
        } else if (domain?.includes('outlook') || domain?.includes('hotmail') || domain?.includes('live') || domain?.includes('msn')) {
            return 'Outlook/Hotmail';
        } else if (domain?.includes('yahoo')) {
            return 'Yahoo';
        } else if (domain?.includes('aol')) {
            return 'AOL';
        } else if (domain?.includes('icloud') || domain?.includes('me.com') || domain?.includes('mac.com')) {
            return 'iCloud';
        } else {
            return 'Other';
        }
    }

    /**
     * Create HTML email template
     */
    createEmailTemplate(content, isHtml = true) {
        if (!isHtml) {
            return content;
        }

        return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SallamChat</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #0ea5e9;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #0ea5e9;
            margin-bottom: 10px;
        }
        .content {
            margin-bottom: 30px;
        }
        .footer {
            text-align: center;
            border-top: 1px solid #eee;
            padding-top: 20px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">🔥 SallamChat</div>
            <p>للتواصل الحر والسلمي</p>
        </div>
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>هذه رسالة من إدارة SallamChat</p>
            <p>© 2024 SallamChat. جميع الحقوق محفوظة.</p>
        </div>
    </div>
</body>
</html>`;
    }
}

// Create global email service instance
window.emailService = new EmailService();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EmailService;
}

console.log('Email service loaded successfully');
