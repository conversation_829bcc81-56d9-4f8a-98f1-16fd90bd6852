<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SallamChat - Admin Login Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .credential-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-button {
            margin: 5px;
        }
        #testResults {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <!-- Header -->
            <div class="test-card text-center">
                <h1 class="text-primary mb-3">
                    <i class="bi bi-shield-check me-2"></i>
                    SallamChat Admin Login Test
                </h1>
                <p class="text-muted">اختبار وظائف تسجيل دخول المدير</p>
            </div>

            <!-- Valid Credentials -->
            <div class="test-card">
                <h3 class="text-success mb-3">
                    <i class="bi bi-check-circle me-2"></i>
                    بيانات الدخول الصحيحة
                </h3>
                
                <div class="credential-item">
                    <strong>المدير الرئيسي:</strong><br>
                    اسم المستخدم: <code>ADMIN</code><br>
                    كلمة المرور: <code>*#admin1981#*</code>
                    <button class="btn btn-success btn-sm test-button" onclick="testLogin('ADMIN', '*#admin1981#*')">
                        اختبار
                    </button>
                </div>

                <div class="credential-item">
                    <strong>مدير بديل:</strong><br>
                    اسم المستخدم: <code>admin</code><br>
                    كلمة المرور: <code>admin123</code>
                    <button class="btn btn-success btn-sm test-button" onclick="testLogin('admin', 'admin123')">
                        اختبار
                    </button>
                </div>

                <div class="credential-item">
                    <strong>مدير SallamChat:</strong><br>
                    اسم المستخدم: <code>sallamchat</code><br>
                    كلمة المرور: <code>admin2024</code>
                    <button class="btn btn-success btn-sm test-button" onclick="testLogin('sallamchat', 'admin2024')">
                        اختبار
                    </button>
                </div>
            </div>

            <!-- Invalid Credentials Test -->
            <div class="test-card">
                <h3 class="text-danger mb-3">
                    <i class="bi bi-x-circle me-2"></i>
                    اختبار بيانات خاطئة
                </h3>
                
                <div class="credential-item">
                    <strong>بيانات خاطئة:</strong><br>
                    اسم المستخدم: <code>wronguser</code><br>
                    كلمة المرور: <code>wrongpass</code>
                    <button class="btn btn-danger btn-sm test-button" onclick="testLogin('wronguser', 'wrongpass')">
                        اختبار
                    </button>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="test-card">
                <h3 class="text-info mb-3">
                    <i class="bi bi-gear me-2"></i>
                    أدوات الاختبار
                </h3>
                
                <div class="d-flex gap-2 mb-3">
                    <button class="btn btn-primary" onclick="testAllCredentials()">
                        <i class="bi bi-play-circle me-2"></i>
                        اختبار جميع البيانات
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">
                        <i class="bi bi-trash me-2"></i>
                        مسح النتائج
                    </button>
                    <button class="btn btn-info" onclick="checkAdminStatus()">
                        <i class="bi bi-info-circle me-2"></i>
                        حالة المدير
                    </button>
                </div>

                <!-- Manual Test Form -->
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="testUsername" placeholder="اسم المستخدم">
                    </div>
                    <div class="col-md-4">
                        <input type="password" class="form-control" id="testPassword" placeholder="كلمة المرور">
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-warning w-100" onclick="testManualLogin()">
                            اختبار يدوي
                        </button>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="test-card">
                <h3 class="text-secondary mb-3">
                    <i class="bi bi-terminal me-2"></i>
                    نتائج الاختبار
                </h3>
                <div id="testResults">
                    جاهز للاختبار...<br>
                    استخدم الأزرار أعلاه لاختبار وظائف تسجيل الدخول.
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/admin.js"></script>

    <script>
        // Test functions
        function log(message) {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            results.innerHTML += `[${timestamp}] ${message}<br>`;
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = 'تم مسح النتائج...<br>';
        }

        async function testLogin(username, password) {
            log(`🔐 اختبار تسجيل الدخول: ${username}`);
            
            try {
                const isValid = await validateAdminCredentials(username, password);
                
                if (isValid) {
                    log(`✅ نجح تسجيل الدخول للمستخدم: ${username}`);
                } else {
                    log(`❌ فشل تسجيل الدخول للمستخدم: ${username}`);
                }
                
                return isValid;
            } catch (error) {
                log(`💥 خطأ في اختبار ${username}: ${error.message}`);
                return false;
            }
        }

        async function testAllCredentials() {
            log('🚀 بدء اختبار جميع بيانات الدخول...');
            
            const credentials = [
                { username: 'ADMIN', password: '*#admin1981#*' },
                { username: 'admin', password: 'admin123' },
                { username: 'sallamchat', password: 'admin2024' },
                { username: 'wronguser', password: 'wrongpass' }
            ];
            
            let passed = 0;
            let failed = 0;
            
            for (const cred of credentials) {
                const result = await testLogin(cred.username, cred.password);
                if (result) {
                    passed++;
                } else {
                    failed++;
                }
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log(`📊 نتائج الاختبار: ${passed} نجح، ${failed} فشل`);
        }

        async function testManualLogin() {
            const username = document.getElementById('testUsername').value.trim();
            const password = document.getElementById('testPassword').value;
            
            if (!username || !password) {
                log('⚠️ يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }
            
            await testLogin(username, password);
        }

        function checkAdminStatus() {
            log('🔍 فحص حالة المدير...');
            
            const savedAdmin = localStorage.getItem('sallamchat_admin');
            if (savedAdmin) {
                try {
                    const admin = JSON.parse(savedAdmin);
                    log(`👤 مدير مسجل الدخول: ${admin.username}`);
                } catch (error) {
                    log('❌ خطأ في قراءة بيانات المدير المحفوظة');
                }
            } else {
                log('👤 لا يوجد مدير مسجل الدخول');
            }
            
            // Check if admin functions are available
            if (typeof validateAdminCredentials === 'function') {
                log('✅ وظائف المدير متاحة');
            } else {
                log('❌ وظائف المدير غير متاحة');
            }
        }

        // Initialize test page
        document.addEventListener('DOMContentLoaded', function() {
            log('🎯 صفحة اختبار المدير جاهزة');
            log('📋 استخدم الأزرار أعلاه لاختبار وظائف تسجيل الدخول');
            checkAdminStatus();
        });
    </script>
</body>
</html>
