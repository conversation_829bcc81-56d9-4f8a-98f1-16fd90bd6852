# SallamChat - للتواصل الحر والسلمي

![SallamChat Logo](assets/images/logo.png)

## 📋 نظرة عامة

SallamChat هو تطبيق محادثة حديث مصمم بتقنية Neumorphism مع دعم كامل للغة العربية. يوفر التطبيق تجربة تواصل سلسة وآمنة مع واجهة مستخدم جذابة وميزات متقدمة.

## ✨ الميزات الرئيسية

### 🎨 واجهة المستخدم
- تصميم Neumorphism عصري وجذاب
- واجهة مستجيبة بالكامل باستخدام Bootstrap 5
- دعم الوضع الليلي والنهاري القابل للتبديل
- أيقونات جميلة من Bootstrap Icons
- خطوط عربية واضحة (Cairo)
- شعار SallamChat مع تدرج لوني من الأزرق السماوي إلى الأخضر الزمردي

### 👤 إدارة المستخدمين
- تسجيل حساب جديد مع التحقق من قوة كلمة المرور
- تسجيل الدخول الآمن
- ميزة "نسيت كلمة المرور"
- صفحة الإعدادات الشخصية لتعديل البيانات

### 💬 وظائف المحادثة
- إرسال واستقبال الرسائل النصية الفورية
- تسجيل وإرسال الرسائل الصوتية
- إجراء مكالمات صوتية ومرئية مجانية
- مشاركة الملفات والصور
- أصوات تنبيه مميزة للرسائل والمكالمات
- التواصل الجماعي (الغرف)
- حفظ المحادثات محلياً

### 🗄️ قاعدة البيانات
- دعم Supabase (الخيار الأساسي للإنتاج) 🔥
- دعم IndexedDB (للاستخدام المحلي المتقدم)
- نظام احتياطي باستخدام LocalStorage
- تخزين آمن للبيانات والمحادثات مع Row Level Security
- إمكانية العمل بدون اتصال بالإنترنت
- **Firebase تم إزالته بالكامل** ❌

### 🔐 لوحة الإدارة
- دخول المدير بالبيانات:
  - اسم المستخدم: `ADMIN`
  - كلمة المرور: `*#admin1981#*`
- عرض جميع المستخدمين
- تعديل بيانات أي مستخدم
- حذف المستخدمين نهائياً
- عرض الإحصائيات العامة (عدد المستخدمين، الرسائل، المكالمات)

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- متصفح ويب حديث يدعم ES6+
- خادم ويب محلي (اختياري للتطوير)

### خطوات التثبيت

1. **استنساخ المشروع**
   ```bash
   git clone https://github.com/your-username/sallamchat.git
   cd sallamchat
   ```

2. **إعداد Firebase (اختياري)**
   - إنشاء مشروع جديد في [Firebase Console](https://console.firebase.google.com/)
   - تفعيل Authentication و Firestore Database
   - نسخ إعدادات Firebase إلى `assets/js/firebase-config.js`

3. **تشغيل التطبيق**
   - افتح `index.html` في المتصفح مباشرة
   - أو استخدم خادم ويب محلي:
     ```bash
     # باستخدام Python
     python -m http.server 8000

     # باستخدام Node.js
     npx serve .

     # باستخدام PHP
     php -S localhost:8000
     ```

## 📁 هيكل المشروع

```
SallamChat/
├── index.html              # صفحة تسجيل الدخول
├── register.html           # صفحة التسجيل
├── chat.html              # واجهة المحادثة الرئيسية
├── admin.html             # لوحة الإدارة
├── assets/
│   ├── css/
│   │   ├── style.css      # الأنماط الرئيسية
│   │   └── themes.css     # أنماط الثيمات
│   ├── js/
│   │   ├── app.js         # الوظائف الرئيسية
│   │   ├── auth.js        # نظام المصادقة
│   │   ├── chat.js        # وظائف المحادثة
│   │   ├── admin.js       # لوحة الإدارة
│   │   ├── supabase-config.js # إعدادات Supabase 🔥
│   │   ├── database-config.js # إعدادات قاعدة البيانات 🆕
│   │   └── indexeddb-wrapper.js # واجهة IndexedDB
│   ├── images/            # الصور والشعارات
│   └── sounds/            # ملفات الصوت
└── README.md              # هذا الملف
```

## 🗄️ هيكل قاعدة البيانات

### IndexedDB (الوضع المحلي)
يستخدم التطبيق IndexedDB كخيار متقدم للتخزين المحلي مع الهيكل التالي:

```
SallamChatDB/
├── users          # بيانات المستخدمين
├── chats          # المحادثات
├── messages       # الرسائل
├── settings       # الإعدادات
├── admins         # بيانات المديرين
└── files          # الملفات المرفقة
```

### مميزات IndexedDB
- **تخزين محلي متقدم**: أكثر قوة من LocalStorage
- **دعم الفهارس**: بحث سريع وفعال
- **تخزين الملفات**: إمكانية حفظ الصور والملفات
- **المعاملات**: ضمان سلامة البيانات
- **العمل بدون اتصال**: وظائف كاملة بدون إنترنت

### ترتيب أولوية قواعد البيانات
1. **Supabase** (الخيار الأساسي للإنتاج) 🔥
2. **IndexedDB** (للاستخدام المحلي المتقدم)
3. **LocalStorage** (كخيار احتياطي أخير)

**ملاحظة**: تم إزالة Firebase بالكامل من التطبيق ❌

## 🔧 الإعدادات

### إعداد Supabase (الخيار الأساسي) 🆕

1. في ملف `assets/js/supabase-config.js`، استبدل الإعدادات التالية:

```javascript
const supabaseConfig = {
    url: 'https://your-project.supabase.co',
    anonKey: 'your-anon-key-here',
    projectId: 'your-project-id'
};
```

2. تم إنشاء الجداول تلقائياً في مشروع Supabase:
   - `users` - بيانات المستخدمين
   - `chats` - المحادثات
   - `messages` - الرسائل
   - `settings` - الإعدادات
   - `admins` - بيانات المديرين

3. تم تطبيق Row Level Security (RLS) لضمان أمان البيانات

### ملاحظة مهمة حول Firebase ❌

**تم إزالة Firebase بالكامل من SallamChat!**

- ❌ لا يوجد دعم لـ Firebase
- ❌ تم حذف جميع ملفات Firebase
- ❌ تم إزالة جميع مراجع Firebase من الكود
- ✅ التطبيق يعتمد الآن على Supabase فقط كقاعدة بيانات سحابية

**البدائل المتاحة:**
- 🔥 **Supabase** - قاعدة البيانات الأساسية (PostgreSQL)
- 💾 **IndexedDB** - للاستخدام المحلي المتقدم
- 📦 **LocalStorage** - كخيار احتياطي أساسي

**مميزات إزالة Firebase:**
- ✅ تقليل حجم التطبيق
- ✅ تحسين الأداء
- ✅ تبسيط الكود
- ✅ التركيز على Supabase كحل موحد

## 🎯 الاستخدام

### للمستخدمين العاديين

1. **التسجيل**: انتقل إلى صفحة التسجيل وأدخل بياناتك
2. **تسجيل الدخول**: استخدم بريدك الإلكتروني وكلمة المرور
3. **بدء محادثة**: اضغط على "محادثة جديدة" وابحث عن مستخدم
4. **إرسال رسائل**: اكتب رسالتك واضغط إرسال
5. **تسجيل صوتي**: اضغط على أيقونة الميكروفون واتحدث
6. **مكالمات**: استخدم أيقونات الهاتف أو الكاميرا للمكالمات

### للمديرين

1. انتقل إلى `/admin.html`
2. سجل الدخول بالبيانات:
   - اسم المستخدم: `ADMIN`
   - كلمة المرور: `*#admin1981#*`
3. استخدم لوحة الإدارة لإدارة المستخدمين والإحصائيات

## 🛠️ التطوير

### إضافة ميزات جديدة

1. **إضافة صفحة جديدة**: أنشئ ملف HTML جديد واربطه بـ CSS و JS
2. **تعديل الأنماط**: عدل في `assets/css/style.css` أو `themes.css`
3. **إضافة وظائف**: أضف الكود في الملفات المناسبة في `assets/js/`

### اختبار التطبيق

- اختبر على متصفحات مختلفة (Chrome, Firefox, Safari, Edge)
- اختبر على أجهزة مختلفة (Desktop, Tablet, Mobile)
- تأكد من عمل جميع الميزات في الوضعين الليلي والنهاري

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **Supabase لا يعمل**: تأكد من صحة إعدادات Supabase في `supabase-config.js`
2. **الأصوات لا تعمل**: تأكد من وجود ملفات الصوت في `assets/sounds/`
3. **الميكروفون لا يعمل**: تأكد من إعطاء الإذن للمتصفح
4. **البيانات لا تحفظ**: تحقق من اتصال Supabase أو استخدم وضع IndexedDB
5. **قاعدة البيانات بطيئة**: تحقق من اتصال الإنترنت أو استخدم الوضع المحلي

### سجلات الأخطاء

افتح Developer Tools في المتصفح (F12) وتحقق من:
- Console للأخطاء البرمجية
- Network لمشاكل الشبكة
- Application للتحقق من LocalStorage

## 📱 التوافق

### المتصفحات المدعومة
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### الأجهزة المدعومة
- أجهزة سطح المكتب
- الأجهزة اللوحية
- الهواتف الذكية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 👨‍💻 المطور

**م. هاني سلام**
- البريد الإلكتروني: [<EMAIL>]
- GitHub: [your-github-profile]

## 🙏 شكر وتقدير

- Bootstrap Team لإطار العمل الرائع
- Supabase Team لقاعدة البيانات المفتوحة المصدر
- مجتمع المطورين العرب للدعم والإلهام
- PostgreSQL Community للقاعدة القوية

---

**SallamChat** - للتواصل الحر والسلمي
© 2024 جميع الحقوق محفوظة
