# SallamChat - للتواصل الحر والسلمي

![SallamChat Logo](assets/images/logo.png)

## 📋 نظرة عامة

SallamChat هو تطبيق محادثة حديث مصمم بتقنية Neumorphism مع دعم كامل للغة العربية. يوفر التطبيق تجربة تواصل سلسة وآمنة مع واجهة مستخدم جذابة وميزات متقدمة.

## ✨ الميزات الرئيسية

### 🎨 واجهة المستخدم
- تصميم Neumorphism عصري وجذاب
- واجهة مستجيبة بالكامل باستخدام Bootstrap 5
- دعم الوضع الليلي والنهاري القابل للتبديل
- أيقونات جميلة من Bootstrap Icons
- خطوط عربية واضحة (Cairo)
- شعار SallamChat مع تدرج لوني من الأزرق السماوي إلى الأخضر الزمردي

### 👤 إدارة المستخدمين
- تسجيل حساب جديد مع التحقق من قوة كلمة المرور
- تسجيل الدخول الآمن
- ميزة "نسيت كلمة المرور"
- صفحة الإعدادات الشخصية لتعديل البيانات

### 💬 وظائف المحادثة
- إرسال واستقبال الرسائل النصية الفورية
- تسجيل وإرسال الرسائل الصوتية
- إجراء مكالمات صوتية ومرئية مجانية
- مشاركة الملفات والصور
- أصوات تنبيه مميزة للرسائل والمكالمات
- التواصل الجماعي (الغرف)
- حفظ المحادثات محلياً

### 🗄️ قاعدة البيانات
- دعم Firebase (مفضل للإنتاج)
- دعم IndexedDB (للاستخدام المحلي المتقدم)
- نظام احتياطي باستخدام LocalStorage
- تخزين آمن للبيانات والمحادثات
- إمكانية العمل بدون اتصال بالإنترنت

### 🔐 لوحة الإدارة
- دخول المدير بالبيانات:
  - اسم المستخدم: `ADMIN`
  - كلمة المرور: `*#admin1981#*`
- عرض جميع المستخدمين
- تعديل بيانات أي مستخدم
- حذف المستخدمين نهائياً
- عرض الإحصائيات العامة (عدد المستخدمين، الرسائل، المكالمات)

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- متصفح ويب حديث يدعم ES6+
- خادم ويب محلي (اختياري للتطوير)

### خطوات التثبيت

1. **استنساخ المشروع**
   ```bash
   git clone https://github.com/your-username/sallamchat.git
   cd sallamchat
   ```

2. **إعداد Firebase (اختياري)**
   - إنشاء مشروع جديد في [Firebase Console](https://console.firebase.google.com/)
   - تفعيل Authentication و Firestore Database
   - نسخ إعدادات Firebase إلى `assets/js/firebase-config.js`

3. **تشغيل التطبيق**
   - افتح `index.html` في المتصفح مباشرة
   - أو استخدم خادم ويب محلي:
     ```bash
     # باستخدام Python
     python -m http.server 8000

     # باستخدام Node.js
     npx serve .

     # باستخدام PHP
     php -S localhost:8000
     ```

## 📁 هيكل المشروع

```
SallamChat/
├── index.html              # صفحة تسجيل الدخول
├── register.html           # صفحة التسجيل
├── chat.html              # واجهة المحادثة الرئيسية
├── admin.html             # لوحة الإدارة
├── assets/
│   ├── css/
│   │   ├── style.css      # الأنماط الرئيسية
│   │   └── themes.css     # أنماط الثيمات
│   ├── js/
│   │   ├── app.js         # الوظائف الرئيسية
│   │   ├── auth.js        # نظام المصادقة
│   │   ├── chat.js        # وظائف المحادثة
│   │   ├── admin.js       # لوحة الإدارة
│   │   ├── indexeddb-wrapper.js # واجهة IndexedDB
│   │   └── firebase-config.js # إعدادات Firebase
│   ├── images/            # الصور والشعارات
│   └── sounds/            # ملفات الصوت
└── README.md              # هذا الملف
```

## 🗄️ هيكل قاعدة البيانات

### IndexedDB (الوضع المحلي)
يستخدم التطبيق IndexedDB كخيار متقدم للتخزين المحلي مع الهيكل التالي:

```
SallamChatDB/
├── users          # بيانات المستخدمين
├── chats          # المحادثات
├── messages       # الرسائل
├── settings       # الإعدادات
├── admins         # بيانات المديرين
└── files          # الملفات المرفقة
```

### مميزات IndexedDB
- **تخزين محلي متقدم**: أكثر قوة من LocalStorage
- **دعم الفهارس**: بحث سريع وفعال
- **تخزين الملفات**: إمكانية حفظ الصور والملفات
- **المعاملات**: ضمان سلامة البيانات
- **العمل بدون اتصال**: وظائف كاملة بدون إنترنت

### ترتيب أولوية قواعد البيانات
1. **Firebase** (إذا كان متاحاً ومُعد)
2. **IndexedDB** (إذا كان مدعوماً في المتصفح)
3. **LocalStorage** (كخيار احتياطي أخير)

## 🔧 الإعدادات

### إعداد Firebase

1. في ملف `assets/js/firebase-config.js`، استبدل الإعدادات التالية:

```javascript
const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
};
```

### إعداد قواعد Firestore

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Chats collection
    match /chats/{chatId} {
      allow read, write: if request.auth != null &&
        request.auth.uid in resource.data.participants;

      match /messages/{messageId} {
        allow read, write: if request.auth != null &&
          request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
      }
    }

    // Admins collection (read-only for authenticated users)
    match /admins/{adminId} {
      allow read: if request.auth != null;
    }
  }
}
```

## 🎯 الاستخدام

### للمستخدمين العاديين

1. **التسجيل**: انتقل إلى صفحة التسجيل وأدخل بياناتك
2. **تسجيل الدخول**: استخدم بريدك الإلكتروني وكلمة المرور
3. **بدء محادثة**: اضغط على "محادثة جديدة" وابحث عن مستخدم
4. **إرسال رسائل**: اكتب رسالتك واضغط إرسال
5. **تسجيل صوتي**: اضغط على أيقونة الميكروفون واتحدث
6. **مكالمات**: استخدم أيقونات الهاتف أو الكاميرا للمكالمات

### للمديرين

1. انتقل إلى `/admin.html`
2. سجل الدخول بالبيانات:
   - اسم المستخدم: `ADMIN`
   - كلمة المرور: `*#admin1981#*`
3. استخدم لوحة الإدارة لإدارة المستخدمين والإحصائيات

## 🛠️ التطوير

### إضافة ميزات جديدة

1. **إضافة صفحة جديدة**: أنشئ ملف HTML جديد واربطه بـ CSS و JS
2. **تعديل الأنماط**: عدل في `assets/css/style.css` أو `themes.css`
3. **إضافة وظائف**: أضف الكود في الملفات المناسبة في `assets/js/`

### اختبار التطبيق

- اختبر على متصفحات مختلفة (Chrome, Firefox, Safari, Edge)
- اختبر على أجهزة مختلفة (Desktop, Tablet, Mobile)
- تأكد من عمل جميع الميزات في الوضعين الليلي والنهاري

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **Firebase لا يعمل**: تأكد من صحة إعدادات Firebase
2. **الأصوات لا تعمل**: تأكد من وجود ملفات الصوت في `assets/sounds/`
3. **الميكروفون لا يعمل**: تأكد من إعطاء الإذن للمتصفح
4. **البيانات لا تحفظ**: تحقق من إعدادات قاعدة البيانات

### سجلات الأخطاء

افتح Developer Tools في المتصفح (F12) وتحقق من:
- Console للأخطاء البرمجية
- Network لمشاكل الشبكة
- Application للتحقق من LocalStorage

## 📱 التوافق

### المتصفحات المدعومة
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### الأجهزة المدعومة
- أجهزة سطح المكتب
- الأجهزة اللوحية
- الهواتف الذكية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 👨‍💻 المطور

**م. هاني سلام**
- البريد الإلكتروني: [<EMAIL>]
- GitHub: [your-github-profile]

## 🙏 شكر وتقدير

- Bootstrap Team لإطار العمل الرائع
- Firebase Team لخدمات قاعدة البيانات
- مجتمع المطورين العرب للدعم والإلهام

---

**SallamChat** - للتواصل الحر والسلمي
© 2024 جميع الحقوق محفوظة
