# SallamChat - دليل Supabase Integration

## 🎯 نظرة عامة

تم تحديث SallamChat ليدعم **Supabase** كخيار أساسي لقاعدة البيانات! Supabase يوفر قاعدة بيانات PostgreSQL قوية مع مصادقة مدمجة وAPI في الوقت الفعلي.

## 🚀 مميزات Supabase في SallamChat

### ✅ **المميزات الجديدة**
- **قاعدة بيانات PostgreSQL**: قوية وموثوقة
- **مصادقة مدمجة**: تسجيل دخول وتسجيل آمن
- **Real-time subscriptions**: تحديثات فورية للرسائل
- **Row Level Security (RLS)**: أمان على مستوى الصفوف
- **API تلقائي**: REST API مُولد تلقائياً
- **تخزين الملفات**: إمكانية رفع وتخزين الملفات

### 🔄 **ترتيب أولوية قواعد البيانات الجديد**
1. **🔥 Supabase** → للإنتاج (الخيار الوحيد)
2. **💾 IndexedDB** → للاستخدام المحلي المتقدم
3. **📦 LocalStorage** → كخيار احتياطي أخير

**ملاحظة**: تم إزالة Firebase بالكامل من التطبيق ❌

## 🏗️ هيكل قاعدة البيانات

### جداول Supabase

```sql
-- جدول المستخدمين
users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE,
    full_name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'offline',
    last_seen TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- جدول المحادثات
chats (
    id UUID PRIMARY KEY,
    participants UUID[],
    last_message TEXT,
    last_message_time TIMESTAMP,
    last_message_sender UUID,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- جدول الرسائل
messages (
    id UUID PRIMARY KEY,
    chat_id UUID REFERENCES chats(id),
    sender_id UUID REFERENCES users(id),
    content TEXT,
    message_type VARCHAR(50) DEFAULT 'text',
    file_url TEXT,
    file_name VARCHAR(255),
    timestamp TIMESTAMP,
    read_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP
)

-- جدول الإعدادات
settings (
    key VARCHAR(255) PRIMARY KEY,
    value JSONB,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- جدول المديرين
admins (
    username VARCHAR(255) PRIMARY KEY,
    password_hash VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)
```

## 🔐 أمان البيانات (RLS Policies)

### سياسات الأمان المطبقة

```sql
-- المستخدمون يمكنهم رؤية وتعديل ملفاتهم الشخصية فقط
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- المستخدمون يمكنهم الوصول للمحادثات التي يشاركون فيها فقط
CREATE POLICY "Users can view own chats" ON chats
    FOR SELECT USING (auth.uid() = ANY(participants));

-- المستخدمون يمكنهم رؤية الرسائل في محادثاتهم فقط
CREATE POLICY "Users can view messages in their chats" ON messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM chats
            WHERE chats.id = messages.chat_id
            AND auth.uid() = ANY(chats.participants)
        )
    );
```

## 🔧 إعداد Supabase

### 1. **معلومات المشروع**
```javascript
const supabaseConfig = {
    url: 'https://dtancaihtgjxkzdmfmrn.supabase.co',
    anonKey: 'your-anon-key-here',
    projectId: 'dtancaihtgjxkzdmfmrn'
};
```

### 2. **تهيئة العميل**
```javascript
// يتم تهيئة Supabase تلقائياً
const supabaseClient = supabase.createClient(supabaseConfig.url, supabaseConfig.anonKey);
```

### 3. **مصادقة المستخدمين**
```javascript
// تسجيل الدخول
const { data, error } = await supabaseAuth.signInWithPassword({
    email: '<EMAIL>',
    password: 'password123'
});

// تسجيل مستخدم جديد
const { data, error } = await supabaseAuth.signUp({
    email: '<EMAIL>',
    password: 'password123'
});
```

## 📊 واجهة البرمجة (API)

### العمليات الأساسية

```javascript
// إضافة مستخدم جديد
const { data, error } = await supabaseClient
    .from('users')
    .insert({
        email: '<EMAIL>',
        full_name: 'اسم المستخدم',
        status: 'online'
    });

// جلب المستخدمين المتصلين
const { data, error } = await supabaseClient
    .from('users')
    .select('*')
    .eq('status', 'online');

// إضافة رسالة جديدة
const { data, error } = await supabaseClient
    .from('messages')
    .insert({
        chat_id: 'chat-uuid',
        sender_id: 'user-uuid',
        content: 'مرحبا',
        message_type: 'text'
    });
```

### التحديثات الفورية (Real-time)

```javascript
// الاستماع للرسائل الجديدة
const subscription = supabaseClient
    .channel('messages')
    .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'messages' },
        (payload) => {
            console.log('رسالة جديدة:', payload.new);
        }
    )
    .subscribe();

// إلغاء الاشتراك
subscription.unsubscribe();
```

## 🔄 التوافق مع Firebase

### واجهة متوافقة
تم إنشاء wrapper يجعل Supabase متوافق مع Firebase API:

```javascript
// نفس الكود يعمل مع Supabase أو Firebase
await window.firebaseAuth.signInWithEmailAndPassword(email, password);
await window.firebaseDB.collection('users').add(userData);
```

### التبديل التلقائي
```javascript
// الأولوية التلقائية (بعد إزالة Firebase):
if (Supabase available) → Use Supabase
else if (IndexedDB supported) → Use IndexedDB
else → Use LocalStorage
```

## 🧪 اختبار Supabase

### في صفحة الاختبار (`test.html`)
1. افتح `test.html` في المتصفح
2. اضغط على "اختبار Supabase"
3. تحقق من النتائج في الرسائل

### اختبار يدوي في وحدة التحكم
```javascript
// فتح وحدة التحكم (F12) وتشغيل:
// اختبار الاتصال
const { data, error } = await window.supabaseClient
    .from('users')
    .select('count', { count: 'exact' });

console.log('عدد المستخدمين:', data);
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في الاتصال**
```javascript
// تحقق من URL و API Key
console.log('Supabase URL:', supabaseConfig.url);
console.log('Supabase Key:', supabaseConfig.anonKey);
```

**2. خطأ في الصلاحيات**
```sql
-- تحقق من RLS policies
SELECT * FROM pg_policies WHERE tablename = 'users';
```

**3. خطأ في المصادقة**
```javascript
// تحقق من حالة المصادقة
const { data: { session } } = await supabaseAuth.getSession();
console.log('Current session:', session);
```

## 📱 لوحة الإدارة

### إحصائيات من Supabase
```javascript
// إحصائيات حقيقية من قاعدة البيانات
const stats = {
    totalUsers: await getUsersCount(),
    totalMessages: await getMessagesCount(),
    onlineUsers: await getOnlineUsersCount()
};
```

### إدارة المستخدمين
```javascript
// عرض جميع المستخدمين
const { data: users } = await supabaseClient
    .from('users')
    .select('*')
    .order('created_at', { ascending: false });

// تحديث حالة المستخدم
await supabaseClient
    .from('users')
    .update({ status: 'suspended' })
    .eq('id', userId);
```

## 🚀 النشر والإنتاج

### 1. **إعداد المتغيرات**
```javascript
// في الإنتاج، استخدم متغيرات البيئة
const supabaseConfig = {
    url: process.env.SUPABASE_URL,
    anonKey: process.env.SUPABASE_ANON_KEY
};
```

### 2. **تحسين الأداء**
```javascript
// استخدم الفهارس للاستعلامات السريعة
CREATE INDEX idx_messages_chat_id ON messages(chat_id);
CREATE INDEX idx_users_status ON users(status);
```

### 3. **النسخ الاحتياطي**
```sql
-- نسخ احتياطي تلقائي في Supabase
-- يتم تلقائياً كل 24 ساعة
```

## 📊 مقارنة الخيارات

| الميزة | Supabase | IndexedDB | LocalStorage |
|--------|----------|-----------|--------------|
| **Real-time** | ✅ | ❌ | ❌ |
| **مصادقة مدمجة** | ✅ | ❌ | ❌ |
| **SQL Support** | ✅ | ❌ | ❌ |
| **Open Source** | ✅ | ✅ | ✅ |
| **Offline Support** | ❌ | ✅ | ✅ |
| **File Storage** | ✅ | ✅ | ❌ |
| **Cost** | 💰 | 🆓 | 🆓 |
| **Scalability** | ✅ | ❌ | ❌ |
| **Security** | ✅ | ⚠️ | ❌ |

## 🎉 الخلاصة

Supabase في SallamChat يوفر:
- **أداء عالي** مع PostgreSQL
- **أمان متقدم** مع RLS
- **تطوير سريع** مع API تلقائي
- **مرونة كاملة** مع SQL
- **تكلفة معقولة** مقارنة بالبدائل

التطبيق الآن جاهز للاستخدام مع **ثلاثة خيارات** لتخزين البيانات، مما يضمن عمله في جميع البيئات والظروف!

**ملاحظة**: تم إزالة Firebase لتبسيط التطبيق والتركيز على Supabase كحل موحد ❌

### 🔗 روابط مفيدة
- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Guide](https://www.postgresql.org/docs/)
- [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
