<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SallamChat - Debug Console</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/themes.css" rel="stylesheet">
    <style>
        .debug-console {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-4">
                    <div class="logo-container">
                        <div class="logo-circle">
                            <i class="bi bi-bug-fill"></i>
                        </div>
                    </div>
                    <h1 class="app-title mt-3">SallamChat Debug</h1>
                    <p class="app-tagline">وحدة تحكم التشخيص والإصلاح</p>
                </div>

                <div class="auth-card">
                    <div class="card-header text-center">
                        <h3>🔍 تشخيص النظام</h3>
                    </div>
                    <div class="card-body">
                        <!-- System Status -->
                        <div class="debug-info">
                            <h5>📊 حالة النظام</h5>
                            <div id="systemStatus">
                                <p><span class="status-indicator" id="supabaseStatus"></span>Supabase: <span id="supabaseText">جاري التحقق...</span></p>
                                <p><span class="status-indicator" id="firebaseStatus"></span>Firebase: <span id="firebaseText">جاري التحقق...</span></p>
                                <p><span class="status-indicator" id="indexeddbStatus"></span>IndexedDB: <span id="indexeddbText">جاري التحقق...</span></p>
                                <p><span class="status-indicator" id="localstorageStatus"></span>LocalStorage: <span id="localstorageText">جاري التحقق...</span></p>
                                <p><span class="status-indicator" id="authStatus"></span>Authentication: <span id="authText">جاري التحقق...</span></p>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="debug-info">
                            <h5>⚡ إجراءات سريعة</h5>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" onclick="checkAllServices()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>فحص جميع الخدمات
                                </button>
                                <button class="btn btn-outline-info" onclick="testRegistration()">
                                    <i class="bi bi-person-plus me-2"></i>اختبار التسجيل
                                </button>
                                <button class="btn btn-outline-success" onclick="testLogin()">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>اختبار تسجيل الدخول
                                </button>
                                <button class="btn btn-outline-warning" onclick="clearAllData()">
                                    <i class="bi bi-trash me-2"></i>مسح جميع البيانات
                                </button>
                            </div>
                        </div>

                        <!-- Debug Console -->
                        <div class="debug-info">
                            <h5>💻 وحدة التحكم</h5>
                            <div class="debug-console" id="debugConsole">
                                SallamChat Debug Console v1.0<br>
                                جاري تهيئة النظام...<br>
                            </div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearConsole()">
                                مسح وحدة التحكم
                            </button>
                        </div>

                        <!-- Navigation -->
                        <div class="text-center mt-4">
                            <a href="index.html" class="btn btn-primary me-2">
                                <i class="bi bi-house me-2"></i>الصفحة الرئيسية
                            </a>
                            <a href="test.html" class="btn btn-outline-primary">
                                <i class="bi bi-check-circle me-2"></i>صفحة الاختبار
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Supabase SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="assets/js/indexeddb-wrapper.js"></script>
    <script src="assets/js/supabase-config.js"></script>
    <script src="assets/js/firebase-config.js"></script>
    <script src="assets/js/app.js"></script>

    <script>
        let debugConsole;

        document.addEventListener('DOMContentLoaded', function() {
            debugConsole = document.getElementById('debugConsole');
            log('🚀 Debug console initialized');

            // Start system check
            setTimeout(checkAllServices, 1000);
        });

        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            debugConsole.innerHTML += `[${timestamp}] ${message}<br>`;
            debugConsole.scrollTop = debugConsole.scrollHeight;
        }

        function setStatus(elementId, status, text) {
            const indicator = document.getElementById(elementId);
            const textElement = document.getElementById(elementId.replace('Status', 'Text'));

            indicator.className = `status-indicator status-${status}`;
            textElement.textContent = text;
        }

        async function checkAllServices() {
            log('🔍 بدء فحص جميع الخدمات...');

            // Check Supabase
            try {
                if (typeof supabase !== 'undefined' && window.supabaseClient) {
                    setStatus('supabaseStatus', 'success', 'متاح ومُعد');
                    log('✅ Supabase: متاح ومُعد');
                } else if (typeof supabase !== 'undefined') {
                    setStatus('supabaseStatus', 'warning', 'متاح لكن غير مُعد');
                    log('⚠️ Supabase: متاح لكن غير مُعد');
                } else {
                    setStatus('supabaseStatus', 'error', 'غير متاح');
                    log('❌ Supabase: غير متاح');
                }
            } catch (error) {
                setStatus('supabaseStatus', 'error', 'خطأ');
                log('❌ Supabase: خطأ - ' + error.message);
            }

            // Check Firebase
            try {
                if (typeof firebase !== 'undefined' && window.firebaseAuth) {
                    setStatus('firebaseStatus', 'success', 'متاح ومُعد');
                    log('✅ Firebase: متاح');
                } else {
                    setStatus('firebaseStatus', 'warning', 'غير متاح');
                    log('⚠️ Firebase: غير متاح');
                }
            } catch (error) {
                setStatus('firebaseStatus', 'error', 'خطأ');
                log('❌ Firebase: خطأ - ' + error.message);
            }

            // Check IndexedDB
            try {
                if (window.indexedDB && window.sallamChatDB) {
                    await window.sallamChatDB.init();
                    if (window.sallamChatDB.isReady) {
                        setStatus('indexeddbStatus', 'success', 'متاح وجاهز');
                        log('✅ IndexedDB: متاح وجاهز');
                    } else {
                        setStatus('indexeddbStatus', 'warning', 'متاح لكن غير جاهز');
                        log('⚠️ IndexedDB: متاح لكن غير جاهز');
                    }
                } else {
                    setStatus('indexeddbStatus', 'error', 'غير مدعوم');
                    log('❌ IndexedDB: غير مدعوم');
                }
            } catch (error) {
                setStatus('indexeddbStatus', 'error', 'خطأ');
                log('❌ IndexedDB: خطأ - ' + error.message);
            }

            // Check LocalStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                setStatus('localstorageStatus', 'success', 'متاح');
                log('✅ LocalStorage: متاح');
            } catch (error) {
                setStatus('localstorageStatus', 'error', 'غير متاح');
                log('❌ LocalStorage: غير متاح - ' + error.message);
            }

            // Check Authentication
            try {
                if (window.firebaseAuth) {
                    setStatus('authStatus', 'success', 'خدمة المصادقة جاهزة');
                    log('✅ Authentication: جاهز');
                } else {
                    setStatus('authStatus', 'warning', 'خدمة المصادقة غير متاحة');
                    log('⚠️ Authentication: غير متاح');
                }
            } catch (error) {
                setStatus('authStatus', 'error', 'خطأ في خدمة المصادقة');
                log('❌ Authentication: خطأ - ' + error.message);
            }

            log('✅ انتهى فحص جميع الخدمات');
        }

        async function testRegistration() {
            log('🧪 اختبار التسجيل...');

            try {
                if (!window.firebaseAuth) {
                    throw new Error('خدمة المصادقة غير متاحة');
                }

                const testEmail = 'test' + Date.now() + '@example.com';
                const testPassword = 'test123456';

                log(`📝 محاولة تسجيل مستخدم: ${testEmail}`);

                const result = await window.firebaseAuth.createUserWithEmailAndPassword(testEmail, testPassword);
                log('✅ نجح التسجيل: ' + result.user.email);

                // Clean up
                await window.firebaseAuth.signOut();
                log('🚪 تم تسجيل الخروج');

            } catch (error) {
                log('❌ فشل اختبار التسجيل: ' + error.message);
            }
        }

        async function testLogin() {
            log('🧪 اختبار تسجيل الدخول...');

            try {
                if (!window.firebaseAuth) {
                    throw new Error('خدمة المصادقة غير متاحة');
                }

                // First create a test user
                const testEmail = 'testlogin' + Date.now() + '@example.com';
                const testPassword = 'test123456';

                log(`📝 إنشاء مستخدم اختبار: ${testEmail}`);
                await window.firebaseAuth.createUserWithEmailAndPassword(testEmail, testPassword);

                // Sign out
                await window.firebaseAuth.signOut();

                // Try to sign in
                log(`🔑 محاولة تسجيل الدخول: ${testEmail}`);
                const result = await window.firebaseAuth.signInWithEmailAndPassword(testEmail, testPassword);
                log('✅ نجح تسجيل الدخول: ' + result.user.email);

                // Clean up
                await window.firebaseAuth.signOut();
                log('🚪 تم تسجيل الخروج');

            } catch (error) {
                log('❌ فشل اختبار تسجيل الدخول: ' + error.message);
            }
        }

        async function clearAllData() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                return;
            }

            log('🗑️ مسح جميع البيانات...');

            try {
                // Clear IndexedDB
                if (window.sallamChatDB && window.sallamChatDB.isReady) {
                    await window.sallamChatDB.clearAllData();
                    log('✅ تم مسح بيانات IndexedDB');
                }

                // Clear LocalStorage
                localStorage.clear();
                log('✅ تم مسح بيانات LocalStorage');

                log('✅ تم مسح جميع البيانات بنجاح');

            } catch (error) {
                log('❌ خطأ في مسح البيانات: ' + error.message);
            }
        }

        function clearConsole() {
            debugConsole.innerHTML = 'SallamChat Debug Console v1.0<br>وحدة التحكم تم مسحها.<br>';
        }

        // Override console methods to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            log('📝 ' + args.join(' '));
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            log('❌ ' + args.join(' '));
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            log('⚠️ ' + args.join(' '));
        };
    </script>
</body>
</html>
