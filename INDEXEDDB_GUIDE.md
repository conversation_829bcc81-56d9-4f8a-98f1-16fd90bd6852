# SallamChat - دليل IndexedDB

## 📋 نظرة عامة

تم تحديث SallamChat ليدعم **IndexedDB** كخيار متقدم لتخزين البيانات محلياً في المتصفح. هذا يوفر تجربة أفضل من LocalStorage مع إمكانيات أكثر تطوراً.

## 🎯 مميزات IndexedDB في SallamChat

### ✅ **المميزات الجديدة**
- **تخزين متقدم**: قاعدة بيانات كاملة في المتصفح
- **فهارس سريعة**: بحث فعال في البيانات
- **تخزين الملفات**: حفظ الصور والملفات الصوتية
- **معاملات آمنة**: ضمان سلامة البيانات
- **عمل بدون اتصال**: جميع الوظائف متاحة محلياً

### 🔄 **ترتيب أولوية قواعد البيانات**
1. **Firebase** → للإنتاج مع الخادم
2. **IndexedDB** → للاستخدام المحلي المتقدم
3. **LocalStorage** → كخيار احتياطي أخير

## 🏗️ هيكل قاعدة البيانات

### Object Stores (الجداول)

```javascript
SallamChatDB (version 1)
├── users          // بيانات المستخدمين
│   ├── id (primary key)
│   ├── email (index, unique)
│   ├── fullName
│   ├── status (index)
│   └── createdAt (index)
│
├── chats          // المحادثات
│   ├── id (primary key)
│   ├── participants (index, multiEntry)
│   ├── lastMessage
│   └── lastMessageTime (index)
│
├── messages       // الرسائل
│   ├── id (primary key)
│   ├── chatId (index)
│   ├── senderId (index)
│   ├── content
│   ├── type (index)
│   └── timestamp (index)
│
├── settings       // الإعدادات
│   ├── key (primary key)
│   └── value
│
├── admins         // بيانات المديرين
│   ├── username (primary key)
│   └── password
│
└── files          // الملفات المرفقة
    ├── id (primary key)
    ├── messageId (index)
    ├── type (index)
    ├── data (base64)
    └── metadata
```

## 🔧 واجهة البرمجة (API)

### الوظائف الأساسية

```javascript
// إضافة بيانات
await window.sallamChatDB.add('users', userData);

// تحديث بيانات
await window.sallamChatDB.update('users', userData);

// جلب بيانات بالمعرف
const user = await window.sallamChatDB.get('users', userId);

// جلب جميع البيانات
const allUsers = await window.sallamChatDB.getAll('users');

// حذف بيانات
await window.sallamChatDB.delete('users', userId);

// البحث بالفهرس
const onlineUsers = await window.sallamChatDB.queryByIndex('users', 'status', 'online');
```

### وظائف متخصصة للمستخدمين

```javascript
// إضافة مستخدم جديد
await window.sallamChatDB.addUser({
    email: '<EMAIL>',
    fullName: 'اسم المستخدم',
    password: 'password123'
});

// البحث بالبريد الإلكتروني
const user = await window.sallamChatDB.getUserByEmail('<EMAIL>');

// تحديث حالة المستخدم
await window.sallamChatDB.updateUserStatus(userId, 'online');
```

### وظائف المحادثات

```javascript
// إضافة محادثة جديدة
await window.sallamChatDB.addChat({
    participants: [userId1, userId2],
    lastMessage: '',
    lastMessageTime: new Date().toISOString()
});

// جلب محادثات المستخدم
const userChats = await window.sallamChatDB.getChatsByUser(userId);

// تحديث آخر رسالة
await window.sallamChatDB.updateChatLastMessage(chatId, 'نص الرسالة', senderId);
```

### وظائف الرسائل

```javascript
// إضافة رسالة جديدة
await window.sallamChatDB.addMessage({
    chatId: 'chat123',
    senderId: 'user456',
    content: 'مرحبا',
    type: 'text',
    timestamp: new Date().toISOString()
});

// جلب رسائل المحادثة
const messages = await window.sallamChatDB.getMessagesByChat(chatId, 50);
```

## 🛠️ التنفيذ التقني

### 1. **التهيئة التلقائية**
```javascript
// يتم تهيئة IndexedDB تلقائياً عند تحميل الصفحة
window.sallamChatDB.init().then(() => {
    console.log('IndexedDB جاهز للاستخدام');
});
```

### 2. **إدارة الأخطاء**
```javascript
try {
    await window.sallamChatDB.addUser(userData);
} catch (error) {
    console.error('خطأ في إضافة المستخدم:', error);
    // التراجع إلى LocalStorage
}
```

### 3. **النسخ الاحتياطي والاستيراد**
```javascript
// تصدير البيانات
const backup = await window.sallamChatDB.exportData();

// استيراد البيانات
await window.sallamChatDB.importData(backup);

// مسح جميع البيانات
await window.sallamChatDB.clearAllData();
```

## 📊 الإحصائيات والمراقبة

```javascript
// جلب إحصائيات شاملة
const stats = await window.sallamChatDB.getStatistics();
console.log(stats);
// {
//   totalUsers: 25,
//   totalMessages: 1250,
//   totalChats: 15,
//   onlineUsers: 5
// }
```

## 🔍 اختبار IndexedDB

### في صفحة الاختبار (`test.html`)
1. افتح `test.html` في المتصفح
2. اضغط على "اختبار IndexedDB"
3. تحقق من النتائج في الرسائل

### اختبار يدوي في وحدة التحكم
```javascript
// فتح وحدة التحكم (F12) وتشغيل:
await window.sallamChatDB.init();

// إضافة مستخدم تجريبي
await window.sallamChatDB.addUser({
    email: '<EMAIL>',
    fullName: 'مستخدم تجريبي',
    password: '123456'
});

// التحقق من البيانات
const users = await window.sallamChatDB.getAll('users');
console.log(users);
```

## 🚀 الاستخدام في الإنتاج

### 1. **للمطورين**
- IndexedDB يعمل تلقائياً بدون إعداد إضافي
- يتم التراجع إلى LocalStorage إذا لم يكن مدعوماً
- جميع البيانات محفوظة محلياً وآمنة

### 2. **للمستخدمين**
- تجربة أسرع وأكثر سلاسة
- العمل بدون اتصال بالإنترنت
- حفظ تلقائي لجميع المحادثات والملفات

### 3. **للمديرين**
- إحصائيات دقيقة ومفصلة
- إدارة كاملة للمستخدمين والبيانات
- نسخ احتياطية سهلة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. IndexedDB غير مدعوم**
```javascript
if (!window.indexedDB) {
    console.warn('IndexedDB غير مدعوم، سيتم استخدام LocalStorage');
}
```

**2. خطأ في فتح قاعدة البيانات**
```javascript
// تحقق من إصدار المتصفح
// امسح بيانات المتصفح وأعد المحاولة
```

**3. مشاكل في الأداء**
```javascript
// استخدم الفهارس للبحث السريع
const results = await window.sallamChatDB.queryByIndex('messages', 'chatId', chatId);
```

## 📱 التوافق

### المتصفحات المدعومة
- ✅ Chrome 24+
- ✅ Firefox 16+
- ✅ Safari 10+
- ✅ Edge 12+
- ✅ Opera 15+

### الأجهزة المدعومة
- ✅ أجهزة سطح المكتب
- ✅ الأجهزة اللوحية
- ✅ الهواتف الذكية
- ✅ PWA (Progressive Web Apps)

## 🎉 الخلاصة

IndexedDB في SallamChat يوفر:
- **أداء محسن** للتطبيق
- **تجربة مستخدم أفضل** مع العمل بدون اتصال
- **مرونة في التطوير** مع API شامل
- **أمان البيانات** مع المعاملات الآمنة

التطبيق الآن جاهز للاستخدام مع ثلاثة خيارات لتخزين البيانات، مما يضمن عمله في جميع البيئات والظروف!
