/* SallamChat - Theme System (Light & Dark Mode) */

/* ===== LIGHT THEME (DEFAULT) ===== */
:root {
    /* Background Colors */
    --bg-light: #f0f4f8;
    --bg-dark: #1a202c;
    --surface-light: #ffffff;
    --surface-dark: #2d3748;
    
    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.3);
    --shadow-inset-light: inset 5px 5px 10px rgba(0, 0, 0, 0.05), inset -5px -5px 10px rgba(255, 255, 255, 0.8);
    --shadow-inset-dark: inset 5px 5px 10px rgba(0, 0, 0, 0.3), inset -5px -5px 10px rgba(255, 255, 255, 0.1);
    
    /* Text Colors */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;
    --text-inverse: #ffffff;
    
    /* Border Colors */
    --border-color: rgba(0, 0, 0, 0.05);
    --border-color-strong: rgba(0, 0, 0, 0.1);
}

/* ===== DARK THEME ===== */
[data-theme="dark"] {
    /* Background Colors */
    --bg-light: #1a202c;
    --bg-dark: #0f1419;
    --surface-light: #2d3748;
    --surface-dark: #1a202c;
    
    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.4);
    --shadow-dark: rgba(0, 0, 0, 0.6);
    --shadow-inset-light: inset 5px 5px 10px rgba(0, 0, 0, 0.4), inset -5px -5px 10px rgba(255, 255, 255, 0.02);
    --shadow-inset-dark: inset 5px 5px 10px rgba(0, 0, 0, 0.6), inset -5px -5px 10px rgba(255, 255, 255, 0.05);
    
    /* Text Colors */
    --text-primary: #f7fafc;
    --text-secondary: #cbd5e0;
    --text-muted: #a0aec0;
    --text-inverse: #2d3748;
    
    /* Border Colors */
    --border-color: rgba(255, 255, 255, 0.1);
    --border-color-strong: rgba(255, 255, 255, 0.2);
}

/* ===== THEME TRANSITION ===== */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* ===== DARK THEME SPECIFIC OVERRIDES ===== */
[data-theme="dark"] body {
    background: var(--bg-light);
    color: var(--text-primary);
}

[data-theme="dark"] .neomorphic {
    background: var(--surface-light);
    box-shadow: 
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .neomorphic:hover {
    box-shadow: 
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .neomorphic-inset {
    background: var(--surface-light);
    box-shadow: var(--shadow-inset-light);
}

[data-theme="dark"] .neomorphic-pressed {
    background: var(--surface-light);
    box-shadow: 
        inset 8px 8px 16px var(--shadow-light),
        inset -8px -8px 16px rgba(255, 255, 255, 0.02);
}

/* ===== DARK THEME LOGO ===== */
[data-theme="dark"] .logo-circle {
    box-shadow: 
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .logo-circle-small {
    box-shadow: 
        4px 4px 8px var(--shadow-light),
        -4px -4px 8px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .logo-circle-large {
    box-shadow: 
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.03);
}

/* ===== DARK THEME CARDS ===== */
[data-theme="dark"] .auth-card {
    background: var(--surface-light);
    box-shadow: 
        15px 15px 30px var(--shadow-light),
        -15px -15px 30px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .auth-card:hover {
    box-shadow: 
        20px 20px 40px var(--shadow-light),
        -20px -20px 40px rgba(255, 255, 255, 0.03);
}

/* ===== DARK THEME FORMS ===== */
[data-theme="dark"] .form-control {
    background: var(--surface-light);
    color: var(--text-primary);
    box-shadow: var(--shadow-inset-light);
}

[data-theme="dark"] .form-control:focus {
    background: var(--surface-light);
    color: var(--text-primary);
    box-shadow: 
        var(--shadow-inset-light),
        0 0 0 3px rgba(14, 165, 233, 0.2);
}

[data-theme="dark"] .form-control::placeholder {
    color: var(--text-muted);
}

[data-theme="dark"] .input-group-text {
    background: var(--surface-light);
    color: var(--text-secondary);
    box-shadow: var(--shadow-inset-light);
    border-color: var(--border-color);
}

/* ===== DARK THEME BUTTONS ===== */
[data-theme="dark"] .btn-primary {
    box-shadow: 
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .btn-primary:hover {
    box-shadow: 
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .btn-primary:active {
    box-shadow: var(--shadow-inset-light);
}

[data-theme="dark"] .btn-outline-primary {
    background: var(--surface-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 
        6px 6px 12px var(--shadow-light),
        -6px -6px 12px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .btn-outline-secondary {
    background: var(--surface-light);
    color: var(--text-secondary);
    border-color: var(--text-muted);
    box-shadow: 
        6px 6px 12px var(--shadow-light),
        -6px -6px 12px rgba(255, 255, 255, 0.02);
}

/* ===== DARK THEME NAVIGATION ===== */
[data-theme="dark"] .chat-navbar,
[data-theme="dark"] .admin-navbar {
    background: var(--surface-light);
    border-bottom-color: var(--border-color);
    box-shadow: 0 4px 8px var(--shadow-light);
}

/* ===== DARK THEME CHAT ===== */
[data-theme="dark"] .chat-sidebar {
    background: var(--surface-light);
    border-right-color: var(--border-color);
    box-shadow: 4px 0 8px var(--shadow-light);
}

[data-theme="dark"] .chat-item {
    background: var(--surface-light);
    box-shadow: 
        4px 4px 8px var(--shadow-light),
        -4px -4px 8px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .chat-item:hover {
    box-shadow: 
        6px 6px 12px var(--shadow-light),
        -6px -6px 12px rgba(255, 255, 255, 0.03);
}

[data-theme="dark"] .welcome-screen,
[data-theme="dark"] .active-chat {
    background: var(--surface-light);
    box-shadow: var(--shadow-inset-light);
}

[data-theme="dark"] .active-chat {
    box-shadow: 
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .chat-header {
    background: var(--surface-light);
    border-bottom-color: var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-light);
}

[data-theme="dark"] .messages-container {
    background: var(--bg-light);
}

[data-theme="dark"] .message.received .message-bubble {
    background: var(--surface-light);
    color: var(--text-primary);
    box-shadow: 
        4px 4px 8px var(--shadow-light),
        -4px -4px 8px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .message-input-container {
    background: var(--surface-light);
    border-top-color: var(--border-color);
    box-shadow: 0 -2px 4px var(--shadow-light);
}

/* ===== DARK THEME ADMIN PANEL ===== */
[data-theme="dark"] .admin-sidebar,
[data-theme="dark"] .admin-content {
    background: var(--surface-light);
    box-shadow: 
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .stat-card {
    background: var(--surface-light);
    box-shadow: 
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .stat-card:hover {
    box-shadow: 
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.03);
}

/* ===== DARK THEME FOOTER ===== */
[data-theme="dark"] .app-footer {
    background: var(--surface-light);
    box-shadow: 0 -8px 16px var(--shadow-light);
    color: var(--text-secondary);
}

/* ===== DARK THEME MODALS ===== */
[data-theme="dark"] .modal-content {
    background: var(--surface-light);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .modal-header {
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modal-footer {
    border-top-color: var(--border-color);
}

/* ===== DARK THEME TABLES ===== */
[data-theme="dark"] .table {
    color: var(--text-primary);
}

[data-theme="dark"] .table th {
    border-top-color: var(--border-color);
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .table td {
    border-top-color: var(--border-color);
}

[data-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* ===== DARK THEME DROPDOWNS ===== */
[data-theme="dark"] .dropdown-menu {
    background: var(--surface-light);
    border-color: var(--border-color);
    box-shadow: 
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .dropdown-item {
    color: var(--text-primary);
}

[data-theme="dark"] .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
}

[data-theme="dark"] .dropdown-divider {
    border-top-color: var(--border-color);
}

/* ===== DARK THEME LOADING SPINNER ===== */
[data-theme="dark"] .loading-spinner {
    background: rgba(26, 32, 44, 0.9);
}

/* ===== THEME TOGGLE BUTTON ===== */
[data-theme="dark"] .theme-toggle {
    background: var(--surface-light);
    color: var(--text-primary);
    box-shadow: 
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .theme-toggle:hover {
    box-shadow: 
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.03);
}
