/**
 * SallamChat - Supabase Configuration
 * This file contains Supabase initialization and configuration
 */

// Supabase configuration
const supabaseConfig = {
    url: 'https://dtancaihtgjxkzdmfmrn.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR0YW5jYWlodGdqeGt6ZG1mbXJuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM2NjQ5MDAsImV4cCI6MjA0OTI0MDkwMH0.placeholder-key-replace-with-actual',
    projectId: 'dtancaihtgjxkzdmfmrn'
};

// Check if Supabase key is valid (not placeholder)
const isSupabaseConfigValid = () => {
    return supabaseConfig.anonKey &&
           !supabaseConfig.anonKey.includes('placeholder') &&
           supabaseConfig.anonKey.length > 100;
};

// Global Supabase client
let supabaseClient = null;
let supabaseAuth = null;
let supabaseDB = null;

/**
 * Initialize Supabase client
 */
async function initializeSupabase() {
    try {
        console.log('🔍 Checking Supabase configuration...');

        // Check if Supabase configuration is valid
        if (!isSupabaseConfigValid()) {
            throw new Error('Supabase configuration contains placeholder keys. Please configure with real Supabase credentials.');
        }

        // Check if Supabase SDK is available
        if (typeof supabase === 'undefined') {
            throw new Error('Supabase SDK not loaded');
        }

        console.log('✅ Supabase configuration valid, initializing...');

        // Create Supabase client
        supabaseClient = supabase.createClient(supabaseConfig.url, supabaseConfig.anonKey);

        if (!supabaseClient) {
            throw new Error('Failed to create Supabase client');
        }

        // Test the connection with a simple query
        console.log('🔗 Testing Supabase connection...');
        const { data, error } = await supabaseClient.from('users').select('count', { count: 'exact' });

        if (error && error.message.includes('Invalid API key')) {
            throw new Error('Invalid Supabase API key. Please check your configuration.');
        }

        // Initialize auth and database references
        supabaseAuth = supabaseClient.auth;
        supabaseDB = supabaseClient;

        console.log('✅ Supabase initialized successfully');

        // Set up database tables if they don't exist
        await setupDatabaseTables();

        // Create auth wrapper
        createSupabaseAuthWrapper();

        // Create database wrapper
        createSupabaseDatabaseWrapper();

        return true;

    } catch (error) {
        console.warn('⚠️ Supabase initialization failed:', error.message);
        throw error;
    }
}

/**
 * Setup database tables
 */
async function setupDatabaseTables() {
    try {
        console.log('Setting up Supabase database tables...');

        // Check if tables exist by trying to query them
        const tables = ['users', 'chats', 'messages', 'settings', 'admins'];

        for (const table of tables) {
            try {
                await supabaseClient.from(table).select('*').limit(1);
                console.log(`✅ Table '${table}' exists`);
            } catch (error) {
                console.warn(`⚠️ Table '${table}' might not exist:`, error.message);
            }
        }

        console.log('Database tables check completed');

    } catch (error) {
        console.error('Error setting up database tables:', error);
    }
}

/**
 * Create Supabase auth wrapper compatible with Firebase auth
 */
function createSupabaseAuthWrapper() {
    window.firebaseAuth = {
        currentUser: null,

        // Initialize current user from Supabase session
        async init() {
            const { data: { session } } = await supabaseAuth.getSession();
            if (session?.user) {
                this.currentUser = {
                    uid: session.user.id,
                    email: session.user.email,
                    displayName: session.user.user_metadata?.full_name || session.user.email,
                    emailVerified: session.user.email_confirmed_at !== null
                };
            }
        },

        // Auth state change listener
        onAuthStateChanged(callback) {
            // Get initial session
            supabaseAuth.getSession().then(({ data: { session } }) => {
                if (session?.user) {
                    this.currentUser = {
                        uid: session.user.id,
                        email: session.user.email,
                        displayName: session.user.user_metadata?.full_name || session.user.email,
                        emailVerified: session.user.email_confirmed_at !== null
                    };
                } else {
                    this.currentUser = null;
                }
                callback(this.currentUser);
            });

            // Listen for auth changes
            supabaseAuth.onAuthStateChange((event, session) => {
                if (session?.user) {
                    this.currentUser = {
                        uid: session.user.id,
                        email: session.user.email,
                        displayName: session.user.user_metadata?.full_name || session.user.email,
                        emailVerified: session.user.email_confirmed_at !== null
                    };
                } else {
                    this.currentUser = null;
                }
                callback(this.currentUser);
            });
        },

        // Sign in with email and password
        async signInWithEmailAndPassword(email, password) {
            const { data, error } = await supabaseAuth.signInWithPassword({
                email: email,
                password: password
            });

            if (error) {
                throw new Error(error.message);
            }

            this.currentUser = {
                uid: data.user.id,
                email: data.user.email,
                displayName: data.user.user_metadata?.full_name || data.user.email,
                emailVerified: data.user.email_confirmed_at !== null
            };

            return { user: this.currentUser };
        },

        // Create user with email and password
        async createUserWithEmailAndPassword(email, password) {
            const { data, error } = await supabaseAuth.signUp({
                email: email,
                password: password
            });

            if (error) {
                throw new Error(error.message);
            }

            this.currentUser = {
                uid: data.user.id,
                email: data.user.email,
                displayName: data.user.user_metadata?.full_name || data.user.email,
                emailVerified: data.user.email_confirmed_at !== null
            };

            return { user: this.currentUser };
        },

        // Sign out
        async signOut() {
            const { error } = await supabaseAuth.signOut();
            if (error) {
                throw new Error(error.message);
            }
            this.currentUser = null;
        },

        // Send password reset email
        async sendPasswordResetEmail(email) {
            const { error } = await supabaseAuth.resetPasswordForEmail(email);
            if (error) {
                throw new Error(error.message);
            }
        },

        // Update user profile
        async updateProfile(updates) {
            const { error } = await supabaseAuth.updateUser({
                data: updates
            });
            if (error) {
                throw new Error(error.message);
            }
        }
    };

    // Initialize current user
    window.firebaseAuth.init();
}

/**
 * Create Supabase database wrapper compatible with Firestore
 */
function createSupabaseDatabaseWrapper() {
    window.firebaseDB = {
        collection(collectionName) {
            return {
                // Add document
                async add(data) {
                    const docData = {
                        ...data,
                        id: generateId(),
                        created_at: new Date().toISOString()
                    };

                    const { data: result, error } = await supabaseClient
                        .from(collectionName)
                        .insert(docData)
                        .select()
                        .single();

                    if (error) {
                        throw new Error(error.message);
                    }

                    return { id: result.id };
                },

                // Get document by ID
                doc(docId) {
                    return {
                        async set(data) {
                            const docData = {
                                ...data,
                                id: docId,
                                updated_at: new Date().toISOString()
                            };

                            const { error } = await supabaseClient
                                .from(collectionName)
                                .upsert(docData);

                            if (error) {
                                throw new Error(error.message);
                            }
                        },

                        async get() {
                            const { data, error } = await supabaseClient
                                .from(collectionName)
                                .select('*')
                                .eq('id', docId)
                                .single();

                            if (error && error.code !== 'PGRST116') {
                                throw new Error(error.message);
                            }

                            return {
                                exists: !!data,
                                data: () => data
                            };
                        },

                        async update(updates) {
                            const updateData = {
                                ...updates,
                                updated_at: new Date().toISOString()
                            };

                            const { error } = await supabaseClient
                                .from(collectionName)
                                .update(updateData)
                                .eq('id', docId);

                            if (error) {
                                throw new Error(error.message);
                            }
                        },

                        async delete() {
                            const { error } = await supabaseClient
                                .from(collectionName)
                                .delete()
                                .eq('id', docId);

                            if (error) {
                                throw new Error(error.message);
                            }
                        },

                        // Sub-collection support
                        collection(subCollectionName) {
                            return {
                                async add(data) {
                                    const docData = {
                                        ...data,
                                        id: generateId(),
                                        parent_id: docId,
                                        created_at: new Date().toISOString()
                                    };

                                    const { data: result, error } = await supabaseClient
                                        .from(subCollectionName)
                                        .insert(docData)
                                        .select()
                                        .single();

                                    if (error) {
                                        throw new Error(error.message);
                                    }

                                    return { id: result.id };
                                },

                                orderBy(field, direction = 'asc') {
                                    return {
                                        onSnapshot(callback) {
                                            // Initial load
                                            const loadData = async () => {
                                                try {
                                                    const { data, error } = await supabaseClient
                                                        .from(subCollectionName)
                                                        .select('*')
                                                        .eq('parent_id', docId)
                                                        .order(field, { ascending: direction === 'asc' });

                                                    if (error) {
                                                        console.error('Error loading data:', error);
                                                        return;
                                                    }

                                                    const snapshot = {
                                                        docs: data.map(doc => ({
                                                            id: doc.id,
                                                            data: () => ({
                                                                ...doc,
                                                                timestamp: doc.timestamp ? { toDate: () => new Date(doc.timestamp) } : null
                                                            })
                                                        }))
                                                    };

                                                    callback(snapshot);
                                                } catch (error) {
                                                    console.error('Error in onSnapshot:', error);
                                                }
                                            };

                                            loadData();

                                            // Set up real-time subscription
                                            const subscription = supabaseClient
                                                .channel(`${subCollectionName}_${docId}`)
                                                .on('postgres_changes',
                                                    {
                                                        event: '*',
                                                        schema: 'public',
                                                        table: subCollectionName,
                                                        filter: `parent_id=eq.${docId}`
                                                    },
                                                    () => loadData()
                                                )
                                                .subscribe();

                                            // Return unsubscribe function
                                            return () => {
                                                subscription.unsubscribe();
                                            };
                                        }
                                    };
                                }
                            };
                        }
                    };
                },

                // Query methods
                where(field, operator, value) {
                    return {
                        async get() {
                            let query = supabaseClient.from(collectionName).select('*');

                            switch (operator) {
                                case '==':
                                    query = query.eq(field, value);
                                    break;
                                case '!=':
                                    query = query.neq(field, value);
                                    break;
                                case '>':
                                    query = query.gt(field, value);
                                    break;
                                case '>=':
                                    query = query.gte(field, value);
                                    break;
                                case '<':
                                    query = query.lt(field, value);
                                    break;
                                case '<=':
                                    query = query.lte(field, value);
                                    break;
                                case 'array-contains':
                                    query = query.contains(field, [value]);
                                    break;
                                default:
                                    query = query.eq(field, value);
                            }

                            const { data, error } = await query;

                            if (error) {
                                throw new Error(error.message);
                            }

                            return {
                                docs: data.map(doc => ({
                                    id: doc.id,
                                    data: () => ({
                                        ...doc,
                                        lastMessageTime: doc.last_message_time ? { toDate: () => new Date(doc.last_message_time) } : null
                                    })
                                }))
                            };
                        },

                        orderBy(field, direction = 'asc') {
                            return {
                                async get() {
                                    let query = supabaseClient.from(collectionName).select('*');

                                    switch (operator) {
                                        case '==':
                                            query = query.eq(field, value);
                                            break;
                                        case 'array-contains':
                                            query = query.contains(field, [value]);
                                            break;
                                        default:
                                            query = query.eq(field, value);
                                    }

                                    query = query.order(field, { ascending: direction === 'asc' });

                                    const { data, error } = await query;

                                    if (error) {
                                        throw new Error(error.message);
                                    }

                                    return {
                                        docs: data.map(doc => ({
                                            id: doc.id,
                                            data: () => ({
                                                ...doc,
                                                lastMessageTime: doc.last_message_time ? { toDate: () => new Date(doc.last_message_time) } : null
                                            })
                                        }))
                                    };
                                }
                            };
                        }
                    };
                },

                orderBy(field, direction = 'asc') {
                    return {
                        async get() {
                            const { data, error } = await supabaseClient
                                .from(collectionName)
                                .select('*')
                                .order(field, { ascending: direction === 'asc' });

                            if (error) {
                                throw new Error(error.message);
                            }

                            return {
                                docs: data.map(doc => ({
                                    id: doc.id,
                                    data: () => doc
                                }))
                            };
                        }
                    };
                },

                async get() {
                    const { data, error } = await supabaseClient
                        .from(collectionName)
                        .select('*');

                    if (error) {
                        throw new Error(error.message);
                    }

                    return {
                        docs: data.map(doc => ({
                            id: doc.id,
                            data: () => doc
                        })),
                        size: data.length
                    };
                },

                onSnapshot(callback) {
                    // Initial load
                    const loadData = async () => {
                        try {
                            const { data, error } = await supabaseClient
                                .from(collectionName)
                                .select('*');

                            if (error) {
                                console.error('Error loading data:', error);
                                return;
                            }

                            const snapshot = {
                                docs: data.map(doc => ({
                                    id: doc.id,
                                    data: () => doc
                                })),
                                docChanges: () => [] // Simplified for now
                            };

                            callback(snapshot);
                        } catch (error) {
                            console.error('Error in collection onSnapshot:', error);
                        }
                    };

                    loadData();

                    // Set up real-time subscription
                    const subscription = supabaseClient
                        .channel(collectionName)
                        .on('postgres_changes',
                            { event: '*', schema: 'public', table: collectionName },
                            () => loadData()
                        )
                        .subscribe();

                    // Return unsubscribe function
                    return () => {
                        subscription.unsubscribe();
                    };
                }
            };
        }
    };
}

/**
 * Generate unique ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// Export Supabase initialization
window.initializeSupabase = initializeSupabase;
window.supabaseClient = supabaseClient;

console.log('Supabase configuration loaded successfully');
