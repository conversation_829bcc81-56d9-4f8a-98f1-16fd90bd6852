/**
 * SallamChat - Database Configuration
 * This application uses SUPABASE ONLY as the database
 * No fallback mechanisms - Supabase is required for operation
 */

// Global database variables
let auth = null;
let db = null;
let storage = null;

console.log('🚀 SallamChat Database Initialization Starting...');
console.log('🔥 Database: SUPABASE ONLY - No Fallbacks');

try {
    // Supabase is REQUIRED - no fallbacks
    if (typeof supabase !== 'undefined' && window.initializeSupabase) {
        console.log('✅ Supabase SDK detected, initializing...');
        window.initializeSupabase().then(() => {
            window.useSupabase = true;
            console.log('🎉 Supabase initialized successfully as ONLY database');

            // Export Supabase services as firebaseAuth/DB for compatibility
            window.firebaseAuth = window.firebaseAuth;
            window.firebaseDB = window.firebaseDB;
            window.firebaseStorage = window.firebaseStorage;

        }).catch(error => {
            console.error('❌ CRITICAL: Supabase initialization failed:', error);
            showDatabaseError('Supabase initialization failed. Please check your configuration.');
        });
        return;
    } else {
        console.error('❌ CRITICAL: Supabase SDK not available');
        showDatabaseError('Supabase SDK not loaded. Please check your internet connection.');
    }

} catch (error) {
    console.error('❌ CRITICAL: Database initialization error:', error);
    showDatabaseError('Database initialization failed. Please refresh the page.');
}

/**
 * Show database error to user
 */
function showDatabaseError(message) {
    console.error('🚨 DATABASE ERROR:', message);

    // Create error overlay
    const errorOverlay = document.createElement('div');
    errorOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(220, 53, 69, 0.95);
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        font-family: Arial, sans-serif;
        text-align: center;
        padding: 20px;
    `;

    errorOverlay.innerHTML = `
        <div style="max-width: 500px;">
            <h1 style="font-size: 2.5rem; margin-bottom: 1rem;">⚠️ خطأ في قاعدة البيانات</h1>
            <h2 style="font-size: 1.5rem; margin-bottom: 2rem;">Database Error</h2>
            <p style="font-size: 1.2rem; margin-bottom: 2rem; line-height: 1.5;">${message}</p>
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin-bottom: 2rem;">
                <h3 style="margin-bottom: 1rem;">🔥 SallamChat يتطلب Supabase</h3>
                <p>هذا التطبيق يعمل بـ Supabase فقط ولا يدعم قواعد بيانات أخرى</p>
                <p>This application requires Supabase and does not support other databases</p>
            </div>
            <button onclick="location.reload()" style="
                background: white;
                color: #dc3545;
                border: none;
                padding: 15px 30px;
                font-size: 1.1rem;
                border-radius: 5px;
                cursor: pointer;
                font-weight: bold;
            ">🔄 إعادة تحميل / Reload</button>
        </div>
    `;

    document.body.appendChild(errorOverlay);

    // Also show browser alert as backup
    setTimeout(() => {
        alert(`SallamChat Database Error:\n\n${message}\n\nThis application requires Supabase to function.`);
    }, 1000);
}

console.log('🔥 SallamChat Database Configuration: SUPABASE ONLY');
