/**
 * SallamChat - Database Configuration
 * This file contains database initialization with Supabase as primary and fallback mechanisms
 * Firebase has been completely removed from this application
 */

// Global database variables
let auth = null;
let db = null;
let storage = null;

console.log('🚀 SallamChat Database Initialization Starting...');
console.log('📋 Database Priority: 1. Supabase → 2. IndexedDB → 3. LocalStorage');

try {
    // Try Supabase first if available
    if (typeof supabase !== 'undefined' && window.initializeSupabase) {
        console.log('✅ Supabase SDK detected, initializing...');
        window.initializeSupabase().then(() => {
            window.useSupabase = true;
            console.log('🎉 Supabase initialized successfully as primary database');
            
            // Export Supabase services as firebaseAuth/DB for compatibility
            window.firebaseAuth = window.firebaseAuth;
            window.firebaseDB = window.firebaseDB;
            window.firebaseStorage = window.firebaseStorage;
            
        }).catch(error => {
            console.error('❌ Supabase initialization failed:', error);
            // Continue with fallback
            initializeFallbackMode();
        });
        return;
    } else {
        console.warn('⚠️ Supabase SDK not available, using fallback mode');
        initializeFallbackMode();
    }

} catch (error) {
    console.error('❌ Database initialization error:', error);
    initializeFallbackMode();
}

function initializeFallbackMode() {
    console.log('🔄 Initializing fallback database mode...');
    
    // Try IndexedDB first, then fallback to localStorage
    if (window.indexedDB && window.sallamChatDB) {
        console.log('💾 IndexedDB available, initializing...');
        initializeIndexedDBMode().catch(err => {
            console.error('❌ IndexedDB initialization failed:', err);
            console.log('📦 Falling back to localStorage...');
            window.mockFirebase = true;
            initializeLocalStorageMode();
        });
    } else {
        console.log('📦 Using localStorage for data persistence...');
        window.mockFirebase = true;
        initializeLocalStorageMode();
    }
}

/**
 * Initialize IndexedDB mode
 */
async function initializeIndexedDBMode() {
    try {
        console.log('🔧 Setting up IndexedDB database...');
        await window.sallamChatDB.init();

        // Create auth wrapper for IndexedDB
        auth = createIndexedDBAuth();
        
        // Create database wrapper for IndexedDB
        db = createIndexedDBDatabase();
        
        // Create storage wrapper for IndexedDB
        storage = createIndexedDBStorage();

        console.log('✅ IndexedDB mode initialized successfully');

        // Export services for compatibility
        window.firebaseAuth = auth;
        window.firebaseDB = db;
        window.firebaseStorage = storage;

    } catch (error) {
        console.error('❌ Failed to initialize IndexedDB:', error);
        throw error;
    }
}

/**
 * Initialize localStorage mode
 */
function initializeLocalStorageMode() {
    console.log('🔧 Setting up localStorage database...');
    
    // Create auth wrapper for localStorage
    auth = createLocalStorageAuth();
    
    // Create database wrapper for localStorage
    db = createLocalStorageDatabase();
    
    // Create storage wrapper for localStorage
    storage = createLocalStorageStorage();

    console.log('✅ localStorage mode initialized successfully');

    // Export services for compatibility
    window.firebaseAuth = auth;
    window.firebaseDB = db;
    window.firebaseStorage = storage;
}

/**
 * Create IndexedDB auth wrapper
 */
function createIndexedDBAuth() {
    return {
        currentUser: null,
        onAuthStateChanged: (callback) => {
            // Check for saved user session
            window.sallamChatDB.getSetting('currentUser').then(savedUser => {
                if (savedUser) {
                    this.currentUser = savedUser;
                    callback(savedUser);
                } else {
                    callback(null);
                }
            }).catch(() => callback(null));
        },
        signInWithEmailAndPassword: async (email, password) => {
            const user = await window.sallamChatDB.getUserByEmail(email);
            if (user && user.password === password) {
                const authUser = {
                    uid: user.id,
                    email: user.email,
                    displayName: user.fullName,
                    emailVerified: true
                };
                this.currentUser = authUser;
                await window.sallamChatDB.setSetting('currentUser', authUser);
                return { user: authUser };
            } else {
                throw new Error('Invalid email or password');
            }
        },
        createUserWithEmailAndPassword: async (email, password) => {
            const existingUser = await window.sallamChatDB.getUserByEmail(email);
            if (existingUser) {
                throw new Error('User already exists');
            }

            const newUser = {
                id: window.sallamChatDB.generateId(),
                email: email,
                password: password,
                fullName: '',
                status: 'active'
            };

            await window.sallamChatDB.addUser(newUser);

            const authUser = {
                uid: newUser.id,
                email: newUser.email,
                displayName: newUser.fullName,
                emailVerified: true
            };

            this.currentUser = authUser;
            await window.sallamChatDB.setSetting('currentUser', authUser);
            return { user: authUser };
        },
        signOut: async () => {
            this.currentUser = null;
            await window.sallamChatDB.setSetting('currentUser', null);
        },
        sendPasswordResetEmail: async (email) => {
            const user = await window.sallamChatDB.getUserByEmail(email);
            if (user) {
                console.log('Password reset email would be sent to:', email);
            } else {
                throw new Error('User not found');
            }
        },
        updateProfile: async (updates) => {
            if (this.currentUser) {
                const updatedUser = { ...this.currentUser, ...updates };
                this.currentUser = updatedUser;
                await window.sallamChatDB.setSetting('currentUser', updatedUser);
            }
        }
    };
}

/**
 * Create localStorage auth wrapper
 */
function createLocalStorageAuth() {
    return {
        currentUser: null,
        onAuthStateChanged: (callback) => {
            // Check localStorage for saved user
            const savedUser = localStorage.getItem('sallamchat_user');
            if (savedUser) {
                const user = JSON.parse(savedUser);
                this.currentUser = user;
                callback(user);
            } else {
                callback(null);
            }
        },
        signInWithEmailAndPassword: (email, password) => {
            return new Promise((resolve, reject) => {
                // Mock authentication - check against localStorage users
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
                const user = users.find(u => u.email === email && u.password === password);

                if (user) {
                    const authUser = {
                        uid: user.id,
                        email: user.email,
                        displayName: user.fullName,
                        emailVerified: true
                    };
                    this.currentUser = authUser;
                    localStorage.setItem('sallamchat_user', JSON.stringify(authUser));
                    resolve({ user: authUser });
                } else {
                    reject(new Error('Invalid email or password'));
                }
            });
        },
        createUserWithEmailAndPassword: (email, password) => {
            return new Promise((resolve, reject) => {
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');

                // Check if user already exists
                if (users.find(u => u.email === email)) {
                    reject(new Error('User already exists'));
                    return;
                }

                const newUser = {
                    id: 'user_' + Date.now(),
                    email: email,
                    password: password,
                    fullName: '',
                    createdAt: new Date().toISOString(),
                    status: 'active'
                };

                users.push(newUser);
                localStorage.setItem('sallamchat_users', JSON.stringify(users));

                const authUser = {
                    uid: newUser.id,
                    email: newUser.email,
                    displayName: newUser.fullName,
                    emailVerified: true
                };

                this.currentUser = authUser;
                localStorage.setItem('sallamchat_user', JSON.stringify(authUser));
                resolve({ user: authUser });
            });
        },
        signOut: () => {
            return new Promise((resolve) => {
                this.currentUser = null;
                localStorage.removeItem('sallamchat_user');
                resolve();
            });
        },
        sendPasswordResetEmail: (email) => {
            return new Promise((resolve, reject) => {
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
                const user = users.find(u => u.email === email);

                if (user) {
                    console.log('Password reset email would be sent to:', email);
                    resolve();
                } else {
                    reject(new Error('User not found'));
                }
            });
        },
        updateProfile: (updates) => {
            return new Promise((resolve) => {
                if (this.currentUser) {
                    const updatedUser = { ...this.currentUser, ...updates };
                    this.currentUser = updatedUser;
                    localStorage.setItem('sallamchat_user', JSON.stringify(updatedUser));
                }
                resolve();
            });
        }
    };
}

// Helper function to convert file to base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = error => reject(error);
    });
}

// Generate unique ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

console.log('📁 Database configuration loaded successfully');
