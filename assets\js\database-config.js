/**
 * SallamChat - Database Configuration
 * Primary: Supabase (for production)
 * Fallback: Local storage system (for development/demo)
 */

// Global database variables
let auth = null;
let db = null;
let storage = null;

console.log('🚀 SallamChat Database Initialization Starting...');

try {
    // Primary: Try Supabase (only if properly configured)
    if (typeof supabase !== 'undefined' && window.initializeSupabase) {
        console.log('✅ Supabase SDK detected, checking configuration...');

        // Check if Supabase is properly configured
        if (typeof isSupabaseConfigValid === 'function' && isSupabaseConfigValid()) {
            console.log('✅ Supabase configuration valid, initializing...');
            window.initializeSupabase().then(() => {
                window.useSupabase = true;
                console.log('🎉 Supabase initialized successfully as primary database');

                // Export Supabase services for compatibility
                window.firebaseAuth = window.firebaseAuth;
                window.firebaseDB = window.firebaseDB;
                window.firebaseStorage = window.firebaseStorage;

            }).catch(error => {
                console.warn('⚠️ Supabase initialization failed, using fallback:', error.message);
                initializeFallbackSystem();
            });
        } else {
            console.warn('⚠️ Supabase configuration invalid (placeholder keys detected), using fallback system');
            initializeFallbackSystem();
        }
    } else {
        console.warn('⚠️ Supabase SDK not available, using fallback system');
        initializeFallbackSystem();
    }

} catch (error) {
    console.warn('⚠️ Database initialization error, using fallback:', error.message);
    initializeFallbackSystem();
}

/**
 * Initialize fallback local storage system
 */
function initializeFallbackSystem() {
    console.log('🔄 Initializing fallback local storage system...');

    window.useSupabase = false;
    window.useFallback = true;

    // Create mock authentication system
    window.firebaseAuth = {
        currentUser: null,
        signInWithEmailAndPassword: async (email, password) => {
            // Simple demo authentication
            const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
            const user = users.find(u => u.email === email && u.password === password);

            if (user) {
                window.firebaseAuth.currentUser = {
                    uid: user.id,
                    email: user.email,
                    displayName: user.fullName
                };
                return { user: window.firebaseAuth.currentUser };
            } else {
                throw new Error('Invalid credentials');
            }
        },
        createUserWithEmailAndPassword: async (email, password) => {
            const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
            const newUser = {
                id: 'user_' + Date.now(),
                email: email,
                password: password,
                fullName: email.split('@')[0],
                createdAt: new Date().toISOString(),
                status: 'active'
            };

            users.push(newUser);
            localStorage.setItem('sallamchat_users', JSON.stringify(users));

            window.firebaseAuth.currentUser = {
                uid: newUser.id,
                email: newUser.email,
                displayName: newUser.fullName
            };

            return { user: window.firebaseAuth.currentUser };
        },
        signOut: async () => {
            window.firebaseAuth.currentUser = null;
        }
    };

    // Create mock database system
    window.firebaseDB = {
        collection: (name) => ({
            doc: (id) => ({
                get: async () => {
                    const data = JSON.parse(localStorage.getItem(`sallamchat_${name}`) || '{}');
                    return {
                        exists: !!data[id],
                        data: () => data[id],
                        id: id
                    };
                },
                set: async (data) => {
                    const collection = JSON.parse(localStorage.getItem(`sallamchat_${name}`) || '{}');
                    collection[id] = data;
                    localStorage.setItem(`sallamchat_${name}`, JSON.stringify(collection));
                },
                update: async (data) => {
                    const collection = JSON.parse(localStorage.getItem(`sallamchat_${name}`) || '{}');
                    collection[id] = { ...collection[id], ...data };
                    localStorage.setItem(`sallamchat_${name}`, JSON.stringify(collection));
                },
                delete: async () => {
                    const collection = JSON.parse(localStorage.getItem(`sallamchat_${name}`) || '{}');
                    delete collection[id];
                    localStorage.setItem(`sallamchat_${name}`, JSON.stringify(collection));
                }
            }),
            get: async () => {
                const data = JSON.parse(localStorage.getItem(`sallamchat_${name}`) || '{}');
                return {
                    docs: Object.keys(data).map(id => ({
                        id: id,
                        data: () => data[id]
                    })),
                    size: Object.keys(data).length
                };
            },
            add: async (data) => {
                const collection = JSON.parse(localStorage.getItem(`sallamchat_${name}`) || '{}');
                const id = 'doc_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                collection[id] = data;
                localStorage.setItem(`sallamchat_${name}`, JSON.stringify(collection));
                return { id: id };
            }
        })
    };

    // Initialize demo data if not exists
    initializeDemoData();

    console.log('✅ Fallback system initialized successfully');
    console.log('📝 Using localStorage for data persistence');
}

/**
 * Initialize demo data for testing
 */
function initializeDemoData() {
    // Initialize demo users if not exists
    if (!localStorage.getItem('sallamchat_users')) {
        const demoUsers = [
            {
                id: 'user_1',
                email: '<EMAIL>',
                password: '123456',
                fullName: 'أحمد محمد',
                createdAt: new Date(Date.now() - 86400000).toISOString(),
                status: 'active'
            },
            {
                id: 'user_2',
                email: '<EMAIL>',
                password: '123456',
                fullName: 'فاطمة علي',
                createdAt: new Date(Date.now() - 172800000).toISOString(),
                status: 'online'
            },
            {
                id: 'user_3',
                email: '<EMAIL>',
                password: '123456',
                fullName: 'محمد أحمد',
                createdAt: new Date(Date.now() - 259200000).toISOString(),
                status: 'active'
            },
            {
                id: 'user_4',
                email: '<EMAIL>',
                password: '123456',
                fullName: 'سارة محمود',
                createdAt: new Date(Date.now() - 345600000).toISOString(),
                status: 'active'
            },
            {
                id: 'user_5',
                email: '<EMAIL>',
                password: '123456',
                fullName: 'علي حسن',
                createdAt: new Date(Date.now() - 432000000).toISOString(),
                status: 'suspended'
            }
        ];

        localStorage.setItem('sallamchat_users', JSON.stringify(demoUsers));
        console.log('📊 Demo users created');
    }

    // Initialize demo messages if not exists
    if (!localStorage.getItem('sallamchat_messages')) {
        const demoMessages = {};
        for (let i = 1; i <= 50; i++) {
            demoMessages[`msg_${i}`] = {
                id: `msg_${i}`,
                text: `رسالة تجريبية رقم ${i}`,
                senderId: `user_${(i % 5) + 1}`,
                timestamp: new Date(Date.now() - (i * 3600000)).toISOString(),
                type: 'text'
            };
        }

        localStorage.setItem('sallamchat_messages', JSON.stringify(demoMessages));
        console.log('💬 Demo messages created');
    }
}

/**
 * Show database warning to user (non-blocking)
 */
function showDatabaseError(message) {
    console.warn('⚠️ DATABASE WARNING:', message);
    console.log('🔄 Fallback system will be used instead');

    // Show a non-blocking notification instead of full-screen error
    if (typeof window !== 'undefined' && window.showAlert) {
        window.showAlert(`تحذير قاعدة البيانات: ${message}. سيتم استخدام النظام البديل.`, 'warning', 5000);
    } else {
        // Fallback notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ffc107;
            color: #212529;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 9999;
            max-width: 400px;
            font-family: Arial, sans-serif;
            border-left: 4px solid #ff9800;
        `;

        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 5px;">⚠️ تحذير قاعدة البيانات</div>
            <div style="font-size: 14px;">${message}</div>
            <div style="font-size: 12px; margin-top: 5px; opacity: 0.8;">سيتم استخدام النظام البديل للتطوير</div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

console.log('🔥 SallamChat Database Configuration: Supabase Primary + LocalStorage Fallback');
