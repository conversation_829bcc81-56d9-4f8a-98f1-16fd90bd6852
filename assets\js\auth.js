/**
 * SallamChat - Authentication JavaScript
 * Handles user login, registration, password reset, and authentication state
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
});

/**
 * Initialize authentication functionality
 */
function initializeAuth() {
    console.log('Initializing authentication system');
    
    // Initialize login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Initialize registration form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegistration);
    }
    
    // Initialize forgot password link
    const forgotPasswordLink = document.getElementById('forgotPassword');
    if (forgotPasswordLink) {
        forgotPasswordLink.addEventListener('click', handleForgotPassword);
    }
    
    // Initialize logout button
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    
    // Initialize password confirmation validation
    initializePasswordConfirmation();
    
    // Check authentication state
    checkAuthState();
}

/**
 * Handle user login
 */
async function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // Validate inputs
    if (!email || !password) {
        window.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    if (!isValidEmail(email)) {
        window.showAlert('يرجى إدخال بريد إلكتروني صحيح', 'warning');
        return;
    }
    
    try {
        window.showLoading();
        
        // Attempt to sign in with Firebase Auth
        const userCredential = await window.firebaseAuth.signInWithEmailAndPassword(email, password);
        const user = userCredential.user;
        
        console.log('User signed in successfully:', user.email);
        
        // Save remember me preference
        if (rememberMe) {
            localStorage.setItem('sallamchat_remember', 'true');
        } else {
            localStorage.removeItem('sallamchat_remember');
        }
        
        // Update user profile if needed
        await updateUserProfile(user);
        
        window.showAlert('تم تسجيل الدخول بنجاح!', 'success', 2000);
        
        // Redirect to chat page
        setTimeout(() => {
            window.location.href = 'chat.html';
        }, 1000);
        
    } catch (error) {
        console.error('Login error:', error);
        
        let errorMessage = 'حدث خطأ أثناء تسجيل الدخول';
        
        switch (error.code) {
            case 'auth/user-not-found':
                errorMessage = 'المستخدم غير موجود';
                break;
            case 'auth/wrong-password':
                errorMessage = 'كلمة المرور غير صحيحة';
                break;
            case 'auth/invalid-email':
                errorMessage = 'البريد الإلكتروني غير صحيح';
                break;
            case 'auth/user-disabled':
                errorMessage = 'تم تعطيل هذا الحساب';
                break;
            case 'auth/too-many-requests':
                errorMessage = 'تم تجاوز عدد المحاولات المسموح. حاول مرة أخرى لاحقاً';
                break;
            default:
                errorMessage = error.message || 'حدث خطأ أثناء تسجيل الدخول';
        }
        
        window.showAlert(errorMessage, 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Handle user registration
 */
async function handleRegistration(event) {
    event.preventDefault();
    
    const fullName = document.getElementById('fullName').value.trim();
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const agreeTerms = document.getElementById('agreeTerms').checked;
    
    // Validate inputs
    if (!fullName || !email || !password || !confirmPassword) {
        window.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    if (fullName.length < 3) {
        window.showAlert('يجب أن يكون الاسم 3 أحرف على الأقل', 'warning');
        return;
    }
    
    if (!isValidEmail(email)) {
        window.showAlert('يرجى إدخال بريد إلكتروني صحيح', 'warning');
        return;
    }
    
    if (password.length < 6) {
        window.showAlert('يجب أن تكون كلمة المرور 6 أحرف على الأقل', 'warning');
        return;
    }
    
    if (password !== confirmPassword) {
        window.showAlert('كلمات المرور غير متطابقة', 'warning');
        return;
    }
    
    if (!agreeTerms) {
        window.showAlert('يجب الموافقة على الشروط والأحكام', 'warning');
        return;
    }
    
    try {
        window.showLoading();
        
        // Create user with Firebase Auth
        const userCredential = await window.firebaseAuth.createUserWithEmailAndPassword(email, password);
        const user = userCredential.user;
        
        console.log('User registered successfully:', user.email);
        
        // Update user profile
        await updateUserDisplayName(user, fullName);
        
        // Save user data to Firestore
        await saveUserData(user.uid, {
            fullName: fullName,
            email: email,
            createdAt: new Date().toISOString(),
            status: 'online',
            lastSeen: new Date().toISOString()
        });
        
        window.showAlert('تم إنشاء الحساب بنجاح!', 'success', 2000);
        
        // Redirect to chat page
        setTimeout(() => {
            window.location.href = 'chat.html';
        }, 1000);
        
    } catch (error) {
        console.error('Registration error:', error);
        
        let errorMessage = 'حدث خطأ أثناء إنشاء الحساب';
        
        switch (error.code) {
            case 'auth/email-already-in-use':
                errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
                break;
            case 'auth/invalid-email':
                errorMessage = 'البريد الإلكتروني غير صحيح';
                break;
            case 'auth/weak-password':
                errorMessage = 'كلمة المرور ضعيفة جداً';
                break;
            default:
                errorMessage = error.message || 'حدث خطأ أثناء إنشاء الحساب';
        }
        
        window.showAlert(errorMessage, 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Handle forgot password
 */
async function handleForgotPassword(event) {
    event.preventDefault();
    
    const email = document.getElementById('email').value.trim();
    
    if (!email) {
        window.showAlert('يرجى إدخال البريد الإلكتروني أولاً', 'warning');
        document.getElementById('email').focus();
        return;
    }
    
    if (!isValidEmail(email)) {
        window.showAlert('يرجى إدخال بريد إلكتروني صحيح', 'warning');
        return;
    }
    
    try {
        window.showLoading();
        
        await window.firebaseAuth.sendPasswordResetEmail(email);
        
        window.showAlert('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني', 'success');
        
    } catch (error) {
        console.error('Password reset error:', error);
        
        let errorMessage = 'حدث خطأ أثناء إرسال رابط إعادة التعيين';
        
        switch (error.code) {
            case 'auth/user-not-found':
                errorMessage = 'البريد الإلكتروني غير مسجل';
                break;
            case 'auth/invalid-email':
                errorMessage = 'البريد الإلكتروني غير صحيح';
                break;
            default:
                errorMessage = error.message || 'حدث خطأ أثناء إرسال رابط إعادة التعيين';
        }
        
        window.showAlert(errorMessage, 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Handle user logout
 */
async function handleLogout(event) {
    event.preventDefault();
    
    try {
        window.showLoading();
        
        // Update user status to offline
        const currentUser = window.firebaseAuth.currentUser;
        if (currentUser) {
            await updateUserStatus(currentUser.uid, 'offline');
        }
        
        // Sign out from Firebase
        await window.firebaseAuth.signOut();
        
        console.log('User signed out successfully');
        
        // Clear local storage
        localStorage.removeItem('sallamchat_remember');
        
        window.showAlert('تم تسجيل الخروج بنجاح', 'success', 2000);
        
        // Redirect to login page
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 1000);
        
    } catch (error) {
        console.error('Logout error:', error);
        window.showAlert('حدث خطأ أثناء تسجيل الخروج', 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Initialize password confirmation validation
 */
function initializePasswordConfirmation() {
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    
    if (passwordInput && confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            const password = passwordInput.value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
                this.classList.add('is-invalid');
            } else {
                this.setCustomValidity('');
                this.classList.remove('is-invalid');
            }
        });
    }
}

/**
 * Check authentication state
 */
function checkAuthState() {
    if (window.firebaseAuth) {
        window.firebaseAuth.onAuthStateChanged(function(user) {
            if (user) {
                // User is signed in
                console.log('User is authenticated:', user.email);
                
                // Update user status to online
                updateUserStatus(user.uid, 'online');
                
                // Update last seen
                updateLastSeen(user.uid);
                
            } else {
                // User is signed out
                console.log('User is not authenticated');
            }
        });
    }
}

/**
 * Update user display name
 */
async function updateUserDisplayName(user, displayName) {
    try {
        if (user.updateProfile) {
            await user.updateProfile({
                displayName: displayName
            });
        }
    } catch (error) {
        console.error('Error updating display name:', error);
    }
}

/**
 * Save user data to database
 */
async function saveUserData(userId, userData) {
    try {
        await window.firebaseDB.collection('users').doc(userId).set(userData);
        console.log('User data saved successfully');
    } catch (error) {
        console.error('Error saving user data:', error);
    }
}

/**
 * Update user profile
 */
async function updateUserProfile(user) {
    try {
        const userDoc = await window.firebaseDB.collection('users').doc(user.uid).get();
        
        if (!userDoc.exists) {
            // Create user profile if it doesn't exist
            await saveUserData(user.uid, {
                fullName: user.displayName || '',
                email: user.email,
                createdAt: new Date().toISOString(),
                status: 'online',
                lastSeen: new Date().toISOString()
            });
        } else {
            // Update existing profile
            await window.firebaseDB.collection('users').doc(user.uid).update({
                status: 'online',
                lastSeen: new Date().toISOString()
            });
        }
    } catch (error) {
        console.error('Error updating user profile:', error);
    }
}

/**
 * Update user status
 */
async function updateUserStatus(userId, status) {
    try {
        await window.firebaseDB.collection('users').doc(userId).update({
            status: status,
            lastSeen: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error updating user status:', error);
    }
}

/**
 * Update last seen timestamp
 */
async function updateLastSeen(userId) {
    try {
        await window.firebaseDB.collection('users').doc(userId).update({
            lastSeen: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error updating last seen:', error);
    }
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Get current user data
 */
async function getCurrentUserData() {
    const user = window.firebaseAuth.currentUser;
    if (!user) return null;
    
    try {
        const userDoc = await window.firebaseDB.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
            return { id: user.uid, ...userDoc.data() };
        }
    } catch (error) {
        console.error('Error getting user data:', error);
    }
    
    return null;
}

// Export functions for global use
window.AuthSystem = {
    getCurrentUserData: getCurrentUserData,
    updateUserStatus: updateUserStatus,
    updateLastSeen: updateLastSeen,
    isValidEmail: isValidEmail
};

console.log('Authentication system loaded successfully');
