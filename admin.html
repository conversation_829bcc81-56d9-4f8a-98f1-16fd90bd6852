<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - SallamChat</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/themes.css" rel="stylesheet">
</head>
<body class="admin-page">
    <!-- Admin Login Screen -->
    <div class="admin-login-screen" id="adminLoginScreen">
        <div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center">
            <div class="row w-100 justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="text-center mb-4">
                        <div class="logo-container">
                            <img src="assets/images/logo.png" alt="SallamChat Logo" class="app-logo"
                                 onerror="this.src='assets/images/logo-fallback.svg'">
                        </div>
                        <h1 class="app-title mt-3">لوحة الإدارة</h1>
                        <p class="app-tagline">SallamChat Admin Panel</p>
                    </div>

                    <div class="auth-card">
                        <div class="card-header text-center">
                            <h3>دخول المدير</h3>
                        </div>
                        <div class="card-body">
                            <form id="adminLoginForm">
                                <div class="mb-3">
                                    <label for="adminUsername" class="form-label">اسم المستخدم</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-person-badge"></i>
                                        </span>
                                        <input type="text" class="form-control" id="adminUsername" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="adminPassword" class="form-label">كلمة المرور</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="bi bi-key"></i>
                                        </span>
                                        <input type="password" class="form-control" id="adminPassword" required>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-danger w-100 mb-3">
                                    <i class="bi bi-shield-check me-2"></i>
                                    دخول الإدارة
                                </button>
                            </form>

                            <div class="text-center">
                                <a href="index.html" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    العودة للصفحة الرئيسية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div class="admin-dashboard d-none" id="adminDashboard">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg admin-navbar">
            <div class="container-fluid">
                <div class="navbar-brand d-flex align-items-center">
                    <img src="assets/images/logo.png" alt="SallamChat Logo" class="navbar-logo me-2"
                         onerror="this.src='assets/images/logo-fallback.svg'">
                    <span class="fw-bold">لوحة إدارة SallamChat</span>
                </div>

                <div class="navbar-nav ms-auto">
                    <button class="btn btn-outline-danger" id="adminLogoutBtn">
                        <i class="bi bi-box-arrow-right me-2"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </nav>

        <div class="container-fluid admin-container">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-3 admin-sidebar">
                    <div class="nav flex-column nav-pills" role="tablist">
                        <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#dashboard-tab">
                            <i class="bi bi-speedometer2 me-2"></i>
                            لوحة المعلومات
                        </button>
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#users-tab">
                            <i class="bi bi-people me-2"></i>
                            إدارة المستخدمين
                        </button>
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#messages-tab">
                            <i class="bi bi-chat-dots me-2"></i>
                            الرسائل
                        </button>
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#statistics-tab">
                            <i class="bi bi-graph-up me-2"></i>
                            الإحصائيات
                        </button>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="col-md-9 admin-content">
                    <div class="tab-content">
                        <!-- Dashboard Tab -->
                        <div class="tab-pane fade show active" id="dashboard-tab">
                            <h4 class="mb-4">لوحة المعلومات</h4>

                            <!-- Statistics Cards -->
                            <div class="row mb-4">
                                <div class="col-md-3 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-people-fill"></i>
                                        </div>
                                        <div class="stat-info">
                                            <h5 id="totalUsers">0</h5>
                                            <p>إجمالي المستخدمين</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-chat-dots-fill"></i>
                                        </div>
                                        <div class="stat-info">
                                            <h5 id="totalMessages">0</h5>
                                            <p>إجمالي الرسائل</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-telephone-fill"></i>
                                        </div>
                                        <div class="stat-info">
                                            <h5 id="totalCalls">0</h5>
                                            <p>إجمالي المكالمات</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-person-check-fill"></i>
                                        </div>
                                        <div class="stat-info">
                                            <h5 id="onlineUsers">0</h5>
                                            <p>المستخدمون المتصلون</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Activity -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">النشاط الأخير</h6>
                                </div>
                                <div class="card-body">
                                    <div id="recentActivity">
                                        <!-- Recent activity will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Users Management Tab -->
                        <div class="tab-pane fade" id="users-tab">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h4>إدارة المستخدمين</h4>
                                <div class="d-flex gap-2 align-items-center">
                                    <button class="btn btn-success" onclick="showEmailModal()">
                                        <i class="bi bi-envelope me-2"></i>إرسال بريد إلكتروني
                                    </button>
                                    <div class="input-group" style="width: 300px;">
                                        <span class="input-group-text">
                                            <i class="bi bi-search"></i>
                                        </span>
                                        <input type="text" class="form-control" placeholder="البحث عن مستخدم..." id="searchUsers">
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>الاسم</th>
                                                    <th>البريد الإلكتروني</th>
                                                    <th>تاريخ التسجيل</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="usersTableBody">
                                                <!-- Users will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Messages Tab -->
                        <div class="tab-pane fade" id="messages-tab">
                            <h4 class="mb-4">مراقبة الرسائل</h4>
                            <div class="card">
                                <div class="card-body">
                                    <div id="messagesMonitor">
                                        <!-- Messages monitoring will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics Tab -->
                        <div class="tab-pane fade" id="statistics-tab">
                            <h4 class="mb-4">الإحصائيات التفصيلية</h4>
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">إحصائيات المستخدمين</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="usersChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">إحصائيات الرسائل</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="messagesChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Edit Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل بيانات المستخدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserId">
                        <div class="mb-3">
                            <label for="editUserName" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="editUserName">
                        </div>
                        <div class="mb-3">
                            <label for="editUserEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="editUserEmail">
                        </div>
                        <div class="mb-3">
                            <label for="editUserStatus" class="form-label">الحالة</label>
                            <select class="form-select" id="editUserStatus">
                                <option value="active">نشط</option>
                                <option value="suspended">معلق</option>
                                <option value="banned">محظور</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveUserChanges">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Modal -->
    <div class="modal fade" id="emailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-envelope me-2"></i>إرسال بريد إلكتروني للمستخدمين
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="emailForm">
                        <!-- Email Configuration -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="emailService" class="form-label">خدمة البريد الإلكتروني</label>
                                <select class="form-select" id="emailService" onchange="updateEmailConfig()">
                                    <option value="smtp">SMTP مخصص</option>
                                    <option value="gmail">Gmail</option>
                                    <option value="outlook">Outlook/Hotmail</option>
                                    <option value="yahoo">Yahoo Mail</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="senderEmail" class="form-label">البريد الإلكتروني للمرسل</label>
                                <input type="email" class="form-control" id="senderEmail" placeholder="<EMAIL>" required>
                            </div>
                        </div>

                        <!-- SMTP Configuration (shown when custom SMTP is selected) -->
                        <div id="smtpConfig" class="row mb-4">
                            <div class="col-md-6">
                                <label for="smtpHost" class="form-label">خادم SMTP</label>
                                <input type="text" class="form-control" id="smtpHost" placeholder="smtp.example.com">
                            </div>
                            <div class="col-md-3">
                                <label for="smtpPort" class="form-label">المنفذ</label>
                                <input type="number" class="form-control" id="smtpPort" placeholder="587" value="587">
                            </div>
                            <div class="col-md-3">
                                <label for="smtpSecurity" class="form-label">الأمان</label>
                                <select class="form-select" id="smtpSecurity">
                                    <option value="tls">TLS</option>
                                    <option value="ssl">SSL</option>
                                    <option value="none">بدون</option>
                                </select>
                            </div>
                        </div>

                        <!-- Authentication -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="emailUsername" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="emailUsername" placeholder="username or email">
                            </div>
                            <div class="col-md-6">
                                <label for="emailPassword" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="emailPassword" placeholder="password or app password">
                                <div class="form-text">
                                    <i class="bi bi-info-circle me-1"></i>
                                    للـ Gmail استخدم App Password، وليس كلمة المرور العادية
                                </div>
                            </div>
                        </div>

                        <!-- Recipients -->
                        <div class="mb-4">
                            <label class="form-label">المستقبلون</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipientType" id="allUsers" value="all" checked onchange="updateRecipients()">
                                        <label class="form-check-label" for="allUsers">
                                            جميع المستخدمين
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipientType" id="activeUsers" value="active" onchange="updateRecipients()">
                                        <label class="form-check-label" for="activeUsers">
                                            المستخدمون النشطون فقط
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="recipientType" id="customEmails" value="custom" onchange="updateRecipients()">
                                        <label class="form-check-label" for="customEmails">
                                            عناوين بريد إلكتروني مخصصة
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div id="recipientCount" class="alert alert-info">
                                        <i class="bi bi-people me-2"></i>
                                        <span id="recipientCountText">جاري تحميل عدد المستقبلين...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Custom Email Addresses (shown when custom is selected) -->
                        <div id="customEmailsDiv" class="mb-4" style="display: none;">
                            <label for="customEmailList" class="form-label">عناوين البريد الإلكتروني</label>
                            <textarea class="form-control" id="customEmailList" rows="4"
                                placeholder="أدخل عناوين البريد الإلكتروني مفصولة بفواصل أو أسطر جديدة:&#10;<EMAIL>, <EMAIL>&#10;<EMAIL>&#10;<EMAIL>"></textarea>
                            <div class="form-text">
                                يمكنك إدخال عناوين من جميع المجالات: Gmail, Hotmail, Outlook, Yahoo, وغيرها
                            </div>
                        </div>

                        <!-- Email Content -->
                        <div class="mb-3">
                            <label for="emailSubject" class="form-label">موضوع الرسالة</label>
                            <input type="text" class="form-control" id="emailSubject" placeholder="موضوع الرسالة" required>
                        </div>

                        <div class="mb-3">
                            <label for="emailBody" class="form-label">محتوى الرسالة</label>
                            <textarea class="form-control" id="emailBody" rows="8" placeholder="اكتب محتوى الرسالة هنا..." required></textarea>
                        </div>

                        <!-- Email Options -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailHtml" checked>
                                    <label class="form-check-label" for="emailHtml">
                                        إرسال كـ HTML
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailSaveConfig">
                                    <label class="form-check-label" for="emailSaveConfig">
                                        حفظ إعدادات البريد الإلكتروني
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Email Preview -->
                        <div class="mb-3">
                            <button type="button" class="btn btn-outline-info" onclick="previewEmail()">
                                <i class="bi bi-eye me-2"></i>معاينة الرسالة
                            </button>
                        </div>

                        <!-- Preview Area -->
                        <div id="emailPreview" class="alert alert-light" style="display: none;">
                            <h6>معاينة الرسالة:</h6>
                            <div id="previewContent"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-info" onclick="testEmailConnection()">
                        <i class="bi bi-wifi me-2"></i>اختبار الاتصال
                    </button>
                    <button type="button" class="btn btn-success" onclick="sendEmails()">
                        <i class="bi bi-send me-2"></i>إرسال الرسائل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Progress Modal -->
    <div class="modal fade" id="emailProgressModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-send me-2"></i>جاري إرسال الرسائل
                    </h5>
                </div>
                <div class="modal-body text-center">
                    <div class="mb-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري الإرسال...</span>
                        </div>
                    </div>
                    <div class="progress mb-3">
                        <div class="progress-bar" id="emailProgress" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="emailStatus">جاري التحضير للإرسال...</div>
                    <div id="emailResults" class="mt-3"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="emailProgressClose" onclick="closeEmailProgress()" disabled>إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Supabase SDK (ONLY Database) -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Custom JS -->
    <script src="assets/js/supabase-config.js"></script>
    <script src="assets/js/database-config.js"></script>
    <script src="assets/js/email-service.js"></script>
    <script src="assets/js/admin.js"></script>
</body>
</html>
