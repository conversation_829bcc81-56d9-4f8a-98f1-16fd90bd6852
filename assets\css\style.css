/* SallamChat - Main Styles with Neumorphism Design */

/* ===== ROOT VARIABLES ===== */
:root {
    /* Primary Colors - Sky Blue to Emerald Green Gradient */
    --primary-gradient: linear-gradient(135deg, #0ea5e9 0%, #10b981 100%);
    --primary-color: #0ea5e9;
    --primary-dark: #0284c7;
    --secondary-color: #10b981;
    --secondary-dark: #059669;

    /* Neumorphism Colors */
    --bg-light: #f0f4f8;
    --bg-dark: #1a202c;
    --surface-light: #ffffff;
    --surface-dark: #2d3748;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.3);
    --shadow-inset-light: inset 5px 5px 10px rgba(0, 0, 0, 0.05), inset -5px -5px 10px rgba(255, 255, 255, 0.8);
    --shadow-inset-dark: inset 5px 5px 10px rgba(0, 0, 0, 0.3), inset -5px -5px 10px rgba(255, 255, 255, 0.1);

    /* Text Colors */
    --text-primary: #2d3748;
    --text-secondary: #718096;
    --text-muted: #a0aec0;

    /* Spacing */
    --border-radius: 15px;
    --border-radius-sm: 10px;
    --border-radius-lg: 25px;

    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: var(--bg-light);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
}

/* ===== NEUMORPHISM COMPONENTS ===== */
.neomorphic {
    background: var(--surface-light);
    border-radius: var(--border-radius);
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.neomorphic:hover {
    box-shadow:
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.9);
}

.neomorphic-inset {
    background: var(--surface-light);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-inset-light);
}

.neomorphic-pressed {
    background: var(--surface-light);
    border-radius: var(--border-radius);
    box-shadow:
        inset 8px 8px 16px var(--shadow-light),
        inset -8px -8px 16px rgba(255, 255, 255, 0.8);
}

/* ===== LOGO STYLES ===== */
.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-circle {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    animation: logoFloat 3s ease-in-out infinite;
}

.logo-circle i {
    font-size: 2.5rem;
    color: white;
}

.logo-circle-small {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        4px 4px 8px var(--shadow-light),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.logo-circle-small i {
    font-size: 1.2rem;
    color: white;
}

.logo-circle-large {
    width: 120px;
    height: 120px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.9);
    margin: 0 auto;
}

.logo-circle-large i {
    font-size: 4rem;
    color: white;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* ===== APP TITLE & TAGLINE ===== */
.app-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 0.5rem;
}

.app-tagline {
    color: var(--text-secondary);
    font-size: 1.1rem;
    text-align: center;
    margin-bottom: 0;
}

/* ===== AUTHENTICATION CARDS ===== */
.auth-card {
    background: var(--surface-light);
    border-radius: var(--border-radius-lg);
    box-shadow:
        15px 15px 30px var(--shadow-light),
        -15px -15px 30px rgba(255, 255, 255, 0.9);
    border: none;
    overflow: hidden;
    transition: var(--transition);
}

.auth-card:hover {
    box-shadow:
        20px 20px 40px var(--shadow-light),
        -20px -20px 40px rgba(255, 255, 255, 0.95);
}

.auth-card .card-header {
    background: transparent;
    border: none;
    padding: 2rem 2rem 1rem;
}

.auth-card .card-body {
    padding: 1rem 2rem 2rem;
}

/* ===== FORM STYLES ===== */
.form-control {
    background: var(--surface-light);
    border: none;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-inset-light);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    background: var(--surface-light);
    border: none;
    box-shadow:
        var(--shadow-inset-light),
        0 0 0 3px rgba(14, 165, 233, 0.1);
}

.input-group-text {
    background: var(--surface-light);
    border: none;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-inset-light);
    color: var(--text-secondary);
}

/* ===== BUTTON STYLES ===== */
.btn {
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.btn-primary:hover {
    background: var(--primary-gradient);
    transform: translateY(-2px);
    box-shadow:
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.9);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-inset-light);
}

.btn-outline-primary {
    background: var(--surface-light);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow:
        6px 6px 12px var(--shadow-light),
        -6px -6px 12px rgba(255, 255, 255, 0.8);
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
}

.btn-outline-secondary {
    background: var(--surface-light);
    color: var(--text-secondary);
    border: 2px solid var(--text-muted);
    box-shadow:
        6px 6px 12px var(--shadow-light),
        -6px -6px 12px rgba(255, 255, 255, 0.8);
}

/* ===== THEME TOGGLE ===== */
.theme-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--surface-light);
    border: none;
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    font-size: 1.2rem;
    transition: var(--transition);
    z-index: 1000;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow:
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.9);
}

/* ===== FOOTER ===== */
.app-footer {
    background: var(--surface-light);
    padding: 2rem 0;
    margin-top: auto;
    box-shadow:
        0 -8px 16px var(--shadow-light);
    color: var(--text-secondary);
}

/* ===== LOADING SPINNER ===== */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(240, 244, 248, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner.show {
    display: flex;
}

/* ===== ALERT CONTAINER ===== */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 400px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .app-title {
        font-size: 2rem;
    }

    .logo-circle {
        width: 60px;
        height: 60px;
    }

    .logo-circle i {
        font-size: 2rem;
    }

    .auth-card .card-header,
    .auth-card .card-body {
        padding: 1.5rem;
    }

    .theme-toggle {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.shadow-inset {
    box-shadow: var(--shadow-inset-light);
}

.border-radius-custom {
    border-radius: var(--border-radius);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.6s ease-out;
}

/* ===== CHAT SPECIFIC STYLES ===== */
.chat-page {
    height: 100vh;
    overflow: hidden;
}

.chat-navbar {
    background: var(--surface-light);
    box-shadow:
        0 4px 8px var(--shadow-light);
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chat-container {
    height: calc(100vh - 80px);
    padding: 0;
}

.chat-sidebar {
    background: var(--surface-light);
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    height: 100%;
    overflow-y: auto;
    box-shadow:
        4px 0 8px var(--shadow-light);
}

.sidebar-header h5 {
    color: var(--text-primary);
    font-weight: 600;
}

.search-container .form-control {
    border-radius: var(--border-radius-sm);
}

.chat-list {
    max-height: calc(100vh - 250px);
    overflow-y: auto;
}

.chat-item {
    background: var(--surface-light);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow:
        4px 4px 8px var(--shadow-light),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.chat-item:hover {
    transform: translateY(-2px);
    box-shadow:
        6px 6px 12px var(--shadow-light),
        -6px -6px 12px rgba(255, 255, 255, 0.9);
}

.chat-item.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-inset-light);
}

.chat-main {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--bg-light);
}

.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--surface-light);
    margin: 1rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-inset-light);
}

.active-chat {
    display: flex;
    flex-direction: column;
    height: 100%;
    margin: 1rem;
    background: var(--surface-light);
    border-radius: var(--border-radius-lg);
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    overflow: hidden;
}

.chat-header {
    background: var(--surface-light);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow:
        0 2px 4px var(--shadow-light);
}

.chat-avatar i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.messages-container {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: var(--bg-light);
}

.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-end;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-sm);
    position: relative;
    box-shadow:
        4px 4px 8px var(--shadow-light),
        -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.message.sent .message-bubble {
    background: var(--primary-gradient);
    color: white;
    border-bottom-right-radius: 4px;
}

.message.received .message-bubble {
    background: var(--surface-light);
    color: var(--text-primary);
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

.message-input-container {
    background: var(--surface-light);
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow:
        0 -2px 4px var(--shadow-light);
}

.voice-recording {
    background: rgba(239, 68, 68, 0.1);
    border-radius: var(--border-radius-sm);
    margin-top: 0.5rem;
}

.recording-animation {
    width: 12px;
    height: 12px;
    background: #ef4444;
    border-radius: 50%;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* ===== ADMIN PANEL STYLES ===== */
.admin-page {
    background: var(--bg-light);
    min-height: 100vh;
}

.admin-navbar {
    background: var(--surface-light);
    box-shadow:
        0 4px 8px var(--shadow-light);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.admin-container {
    padding: 2rem 0;
}

.admin-sidebar {
    background: var(--surface-light);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-right: 1rem;
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.admin-content {
    background: var(--surface-light);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.stat-card {
    background: var(--surface-light);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow:
        8px 8px 16px var(--shadow-light),
        -8px -8px 16px rgba(255, 255, 255, 0.8);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow:
        12px 12px 24px var(--shadow-light),
        -12px -12px 24px rgba(255, 255, 255, 0.9);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.stat-info h5 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-info p {
    color: var(--text-secondary);
    margin: 0;
}

/* ===== PASSWORD STRENGTH INDICATOR ===== */
.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.strength-fill {
    height: 100%;
    transition: var(--transition);
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: #ef4444;
}

.strength-fill.fair {
    width: 50%;
    background: #f59e0b;
}

.strength-fill.good {
    width: 75%;
    background: #10b981;
}

.strength-fill.strong {
    width: 100%;
    background: #059669;
}

.strength-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* ===== RESPONSIVE DESIGN FOR CHAT ===== */
@media (max-width: 768px) {
    .chat-container {
        height: calc(100vh - 70px);
    }

    .chat-sidebar {
        position: fixed;
        left: -100%;
        top: 70px;
        width: 80%;
        height: calc(100vh - 70px);
        z-index: 1000;
        transition: var(--transition);
    }

    .chat-sidebar.show {
        left: 0;
    }

    .chat-main {
        margin: 0.5rem;
    }

    .active-chat {
        margin: 0.5rem;
    }

    .message-bubble {
        max-width: 85%;
    }

    .admin-sidebar {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}
