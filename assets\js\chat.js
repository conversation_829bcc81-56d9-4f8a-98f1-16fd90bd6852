/**
 * SallamChat - Chat Functionality JavaScript
 * Handles messaging, voice recording, file sharing, and real-time communication
 */

// Global chat variables
let currentChatId = null;
let currentChatUser = null;
let messagesListener = null;
let mediaRecorder = null;
let recordingChunks = [];
let recordingTimer = null;
let recordingStartTime = null;

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
});

/**
 * Initialize chat functionality
 */
function initializeChat() {
    console.log('Initializing chat system');
    
    // Check if user is authenticated
    if (!window.firebaseAuth.currentUser) {
        window.location.href = 'index.html';
        return;
    }
    
    // Initialize chat UI elements
    initializeChatUI();
    
    // Load user data and chats
    loadUserData();
    loadChatList();
    
    // Setup real-time listeners
    setupRealtimeListeners();
}

/**
 * Initialize chat UI elements
 */
function initializeChatUI() {
    // New chat button
    const newChatBtn = document.getElementById('newChatBtn');
    if (newChatBtn) {
        newChatBtn.addEventListener('click', showNewChatModal);
    }
    
    // Message input and send button
    const messageInput = document.getElementById('messageInput');
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    
    if (messageInput && sendMessageBtn) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        sendMessageBtn.addEventListener('click', sendMessage);
    }
    
    // Voice recording button
    const voiceRecordBtn = document.getElementById('voiceRecordBtn');
    if (voiceRecordBtn) {
        voiceRecordBtn.addEventListener('click', toggleVoiceRecording);
    }
    
    // Stop recording button
    const stopRecordingBtn = document.getElementById('stopRecordingBtn');
    if (stopRecordingBtn) {
        stopRecordingBtn.addEventListener('click', stopVoiceRecording);
    }
    
    // File attachment button
    const attachFileBtn = document.getElementById('attachFileBtn');
    const fileInput = document.getElementById('fileInput');
    
    if (attachFileBtn && fileInput) {
        attachFileBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileAttachment);
    }
    
    // Voice and video call buttons
    const voiceCallBtn = document.getElementById('voiceCallBtn');
    const videoCallBtn = document.getElementById('videoCallBtn');
    
    if (voiceCallBtn) {
        voiceCallBtn.addEventListener('click', () => initiateCall('voice'));
    }
    
    if (videoCallBtn) {
        videoCallBtn.addEventListener('click', () => initiateCall('video'));
    }
    
    // Search functionality
    const searchChats = document.getElementById('searchChats');
    if (searchChats) {
        searchChats.addEventListener('input', filterChatList);
    }
    
    // Profile settings
    const profileSettings = document.getElementById('profileSettings');
    if (profileSettings) {
        profileSettings.addEventListener('click', showProfileModal);
    }
}

/**
 * Load current user data
 */
async function loadUserData() {
    try {
        const user = window.firebaseAuth.currentUser;
        if (!user) return;
        
        const userDoc = await window.firebaseDB.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
            const userData = userDoc.data();
            
            // Update display name in navbar
            const userDisplayName = document.getElementById('userDisplayName');
            if (userDisplayName) {
                userDisplayName.textContent = userData.fullName || user.email;
            }
        }
    } catch (error) {
        console.error('Error loading user data:', error);
    }
}

/**
 * Load chat list
 */
async function loadChatList() {
    try {
        const user = window.firebaseAuth.currentUser;
        if (!user) return;
        
        const chatList = document.getElementById('chatList');
        if (!chatList) return;
        
        // Get user's chats
        const chatsQuery = await window.firebaseDB
            .collection('chats')
            .where('participants', 'array-contains', user.uid)
            .orderBy('lastMessageTime', 'desc')
            .get();
        
        chatList.innerHTML = '';
        
        if (chatsQuery.docs.length === 0) {
            chatList.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="bi bi-chat-dots fs-1 mb-3"></i>
                    <p>لا توجد محادثات بعد</p>
                    <p class="small">ابدأ محادثة جديدة من الزر أعلاه</p>
                </div>
            `;
            return;
        }
        
        for (const doc of chatsQuery.docs) {
            const chatData = doc.data();
            const chatElement = await createChatListItem(doc.id, chatData);
            chatList.appendChild(chatElement);
        }
        
    } catch (error) {
        console.error('Error loading chat list:', error);
        window.showAlert('حدث خطأ في تحميل المحادثات', 'danger');
    }
}

/**
 * Create chat list item element
 */
async function createChatListItem(chatId, chatData) {
    const user = window.firebaseAuth.currentUser;
    const otherUserId = chatData.participants.find(id => id !== user.uid);
    
    // Get other user's data
    let otherUserData = { fullName: 'مستخدم', status: 'offline' };
    try {
        const otherUserDoc = await window.firebaseDB.collection('users').doc(otherUserId).get();
        if (otherUserDoc.exists) {
            otherUserData = otherUserDoc.data();
        }
    } catch (error) {
        console.error('Error getting other user data:', error);
    }
    
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item';
    chatItem.dataset.chatId = chatId;
    
    const statusClass = otherUserData.status === 'online' ? 'text-success' : 'text-muted';
    const lastMessageTime = chatData.lastMessageTime ? 
        window.SallamChat.formatDate(chatData.lastMessageTime.toDate()) : '';
    
    chatItem.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="chat-avatar me-3">
                <i class="bi bi-person-circle fs-2 ${statusClass}"></i>
            </div>
            <div class="chat-info flex-grow-1">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-1">${window.SallamChat.sanitizeHTML(otherUserData.fullName)}</h6>
                    <small class="text-muted">${lastMessageTime}</small>
                </div>
                <p class="mb-0 text-muted small">${window.SallamChat.sanitizeHTML(chatData.lastMessage || 'لا توجد رسائل')}</p>
            </div>
        </div>
    `;
    
    chatItem.addEventListener('click', () => openChat(chatId, otherUserData));
    
    return chatItem;
}

/**
 * Open a chat conversation
 */
async function openChat(chatId, otherUser) {
    currentChatId = chatId;
    currentChatUser = otherUser;
    
    // Update UI
    const welcomeScreen = document.getElementById('welcomeScreen');
    const activeChat = document.getElementById('activeChat');
    
    if (welcomeScreen) welcomeScreen.style.display = 'none';
    if (activeChat) {
        activeChat.classList.remove('d-none');
        activeChat.style.display = 'flex';
    }
    
    // Update chat header
    const activeChatName = document.getElementById('activeChatName');
    const activeChatStatus = document.getElementById('activeChatStatus');
    
    if (activeChatName) {
        activeChatName.textContent = otherUser.fullName;
    }
    
    if (activeChatStatus) {
        const statusText = otherUser.status === 'online' ? 'متصل' : 'غير متصل';
        activeChatStatus.textContent = statusText;
        activeChatStatus.className = otherUser.status === 'online' ? 'text-success' : 'text-muted';
    }
    
    // Update active chat item
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });
    
    const activeChatItem = document.querySelector(`[data-chat-id="${chatId}"]`);
    if (activeChatItem) {
        activeChatItem.classList.add('active');
    }
    
    // Load messages
    loadMessages(chatId);
}

/**
 * Load messages for a chat
 */
function loadMessages(chatId) {
    // Clear existing listener
    if (messagesListener) {
        messagesListener();
    }
    
    const messagesContainer = document.getElementById('messagesContainer');
    if (!messagesContainer) return;
    
    // Show loading
    messagesContainer.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    // Setup real-time listener for messages
    messagesListener = window.firebaseDB
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', 'asc')
        .onSnapshot((snapshot) => {
            messagesContainer.innerHTML = '';
            
            snapshot.docs.forEach(doc => {
                const messageData = doc.data();
                const messageElement = createMessageElement(messageData);
                messagesContainer.appendChild(messageElement);
            });
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, (error) => {
            console.error('Error loading messages:', error);
            messagesContainer.innerHTML = `
                <div class="text-center text-danger py-4">
                    <i class="bi bi-exclamation-triangle fs-1 mb-3"></i>
                    <p>حدث خطأ في تحميل الرسائل</p>
                </div>
            `;
        });
}

/**
 * Create message element
 */
function createMessageElement(messageData) {
    const user = window.firebaseAuth.currentUser;
    const isOwn = messageData.senderId === user.uid;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isOwn ? 'sent' : 'received'}`;
    
    const messageTime = messageData.timestamp ? 
        window.SallamChat.formatDate(messageData.timestamp.toDate()) : '';
    
    let messageContent = '';
    
    switch (messageData.type) {
        case 'text':
            messageContent = window.SallamChat.sanitizeHTML(messageData.content);
            break;
        case 'voice':
            messageContent = `
                <audio controls class="w-100">
                    <source src="${messageData.fileUrl}" type="audio/webm">
                    رسالة صوتية
                </audio>
            `;
            break;
        case 'file':
            messageContent = `
                <div class="file-attachment">
                    <i class="bi bi-file-earmark me-2"></i>
                    <a href="${messageData.fileUrl}" target="_blank" class="text-decoration-none">
                        ${messageData.fileName}
                    </a>
                </div>
            `;
            break;
        default:
            messageContent = window.SallamChat.sanitizeHTML(messageData.content || '');
    }
    
    messageDiv.innerHTML = `
        <div class="message-bubble">
            ${messageContent}
            <div class="message-time">${messageTime}</div>
        </div>
    `;
    
    return messageDiv;
}

/**
 * Send a text message
 */
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    if (!messageInput || !currentChatId) return;
    
    const content = messageInput.value.trim();
    if (!content) return;
    
    try {
        const user = window.firebaseAuth.currentUser;
        const messageData = {
            senderId: user.uid,
            content: content,
            type: 'text',
            timestamp: new Date(),
            read: false
        };
        
        // Add message to chat
        await window.firebaseDB
            .collection('chats')
            .doc(currentChatId)
            .collection('messages')
            .add(messageData);
        
        // Update chat's last message
        await window.firebaseDB
            .collection('chats')
            .doc(currentChatId)
            .update({
                lastMessage: content,
                lastMessageTime: new Date(),
                lastMessageSender: user.uid
            });
        
        // Clear input
        messageInput.value = '';
        
        // Play send sound
        window.SallamChat.playNotificationSound('message-send');
        
    } catch (error) {
        console.error('Error sending message:', error);
        window.showAlert('حدث خطأ في إرسال الرسالة', 'danger');
    }
}

/**
 * Toggle voice recording
 */
async function toggleVoiceRecording() {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
        stopVoiceRecording();
    } else {
        startVoiceRecording();
    }
}

/**
 * Start voice recording
 */
async function startVoiceRecording() {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        
        mediaRecorder = new MediaRecorder(stream);
        recordingChunks = [];
        recordingStartTime = Date.now();
        
        mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                recordingChunks.push(event.data);
            }
        };
        
        mediaRecorder.onstop = handleRecordingStop;
        
        mediaRecorder.start();
        
        // Update UI
        const voiceRecording = document.getElementById('voiceRecording');
        const voiceRecordBtn = document.getElementById('voiceRecordBtn');
        
        if (voiceRecording) voiceRecording.classList.remove('d-none');
        if (voiceRecordBtn) voiceRecordBtn.disabled = true;
        
        // Start timer
        startRecordingTimer();
        
    } catch (error) {
        console.error('Error starting voice recording:', error);
        window.showAlert('لا يمكن الوصول إلى الميكروفون', 'danger');
    }
}

/**
 * Stop voice recording
 */
function stopVoiceRecording() {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
        
        // Stop all tracks
        mediaRecorder.stream.getTracks().forEach(track => track.stop());
        
        // Update UI
        const voiceRecording = document.getElementById('voiceRecording');
        const voiceRecordBtn = document.getElementById('voiceRecordBtn');
        
        if (voiceRecording) voiceRecording.classList.add('d-none');
        if (voiceRecordBtn) voiceRecordBtn.disabled = false;
        
        // Stop timer
        stopRecordingTimer();
    }
}

/**
 * Handle recording stop
 */
async function handleRecordingStop() {
    if (recordingChunks.length === 0) return;
    
    try {
        const audioBlob = new Blob(recordingChunks, { type: 'audio/webm' });
        
        // Upload audio file
        const fileName = `voice_${Date.now()}.webm`;
        const fileUrl = await uploadFile(audioBlob, fileName);
        
        // Send voice message
        await sendVoiceMessage(fileUrl);
        
    } catch (error) {
        console.error('Error handling recording:', error);
        window.showAlert('حدث خطأ في إرسال الرسالة الصوتية', 'danger');
    }
}

/**
 * Send voice message
 */
async function sendVoiceMessage(fileUrl) {
    if (!currentChatId) return;
    
    try {
        const user = window.firebaseAuth.currentUser;
        const messageData = {
            senderId: user.uid,
            type: 'voice',
            fileUrl: fileUrl,
            timestamp: new Date(),
            read: false
        };
        
        // Add message to chat
        await window.firebaseDB
            .collection('chats')
            .doc(currentChatId)
            .collection('messages')
            .add(messageData);
        
        // Update chat's last message
        await window.firebaseDB
            .collection('chats')
            .doc(currentChatId)
            .update({
                lastMessage: 'رسالة صوتية',
                lastMessageTime: new Date(),
                lastMessageSender: user.uid
            });
        
        // Play send sound
        window.SallamChat.playNotificationSound('message-send');
        
    } catch (error) {
        console.error('Error sending voice message:', error);
        window.showAlert('حدث خطأ في إرسال الرسالة الصوتية', 'danger');
    }
}

/**
 * Start recording timer
 */
function startRecordingTimer() {
    const recordingTime = document.getElementById('recordingTime');
    if (!recordingTime) return;
    
    recordingTimer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        recordingTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

/**
 * Stop recording timer
 */
function stopRecordingTimer() {
    if (recordingTimer) {
        clearInterval(recordingTimer);
        recordingTimer = null;
    }
    
    const recordingTime = document.getElementById('recordingTime');
    if (recordingTime) {
        recordingTime.textContent = '00:00';
    }
}

/**
 * Handle file attachment
 */
async function handleFileAttachment(event) {
    const file = event.target.files[0];
    if (!file || !currentChatId) return;
    
    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
        window.showAlert('حجم الملف كبير جداً (الحد الأقصى 10 ميجابايت)', 'warning');
        return;
    }
    
    try {
        window.showLoading();
        
        // Upload file
        const fileName = `${Date.now()}_${file.name}`;
        const fileUrl = await uploadFile(file, fileName);
        
        // Send file message
        await sendFileMessage(fileUrl, file.name);
        
        // Clear file input
        event.target.value = '';
        
    } catch (error) {
        console.error('Error uploading file:', error);
        window.showAlert('حدث خطأ في رفع الملف', 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Upload file to storage
 */
async function uploadFile(file, fileName) {
    try {
        const storageRef = window.firebaseStorage.ref(`chat_files/${fileName}`);
        const uploadTask = await storageRef.put(file);
        const downloadURL = await uploadTask.ref.getDownloadURL();
        return downloadURL;
    } catch (error) {
        console.error('Error uploading file:', error);
        throw error;
    }
}

/**
 * Send file message
 */
async function sendFileMessage(fileUrl, fileName) {
    if (!currentChatId) return;
    
    try {
        const user = window.firebaseAuth.currentUser;
        const messageData = {
            senderId: user.uid,
            type: 'file',
            fileUrl: fileUrl,
            fileName: fileName,
            timestamp: new Date(),
            read: false
        };
        
        // Add message to chat
        await window.firebaseDB
            .collection('chats')
            .doc(currentChatId)
            .collection('messages')
            .add(messageData);
        
        // Update chat's last message
        await window.firebaseDB
            .collection('chats')
            .doc(currentChatId)
            .update({
                lastMessage: `ملف: ${fileName}`,
                lastMessageTime: new Date(),
                lastMessageSender: user.uid
            });
        
        // Play send sound
        window.SallamChat.playNotificationSound('message-send');
        
    } catch (error) {
        console.error('Error sending file message:', error);
        window.showAlert('حدث خطأ في إرسال الملف', 'danger');
    }
}

/**
 * Show new chat modal
 */
function showNewChatModal() {
    const modal = new bootstrap.Modal(document.getElementById('newChatModal'));
    modal.show();
    
    // Focus on search input
    setTimeout(() => {
        const searchInput = document.getElementById('searchUsers');
        if (searchInput) searchInput.focus();
    }, 500);
}

/**
 * Show profile modal
 */
async function showProfileModal() {
    try {
        const user = window.firebaseAuth.currentUser;
        if (!user) return;
        
        const userDoc = await window.firebaseDB.collection('users').doc(user.uid).get();
        if (userDoc.exists) {
            const userData = userDoc.data();
            
            // Populate form
            document.getElementById('profileName').value = userData.fullName || '';
            document.getElementById('profileEmail').value = user.email;
            document.getElementById('profileStatus').value = userData.status || 'online';
        }
        
        const modal = new bootstrap.Modal(document.getElementById('profileModal'));
        modal.show();
        
    } catch (error) {
        console.error('Error loading profile data:', error);
        window.showAlert('حدث خطأ في تحميل بيانات الملف الشخصي', 'danger');
    }
}

/**
 * Filter chat list
 */
function filterChatList() {
    const searchTerm = document.getElementById('searchChats').value.toLowerCase();
    const chatItems = document.querySelectorAll('.chat-item');
    
    chatItems.forEach(item => {
        const chatName = item.querySelector('h6').textContent.toLowerCase();
        const lastMessage = item.querySelector('p').textContent.toLowerCase();
        
        if (chatName.includes(searchTerm) || lastMessage.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

/**
 * Initiate voice or video call
 */
function initiateCall(type) {
    if (!currentChatUser) return;
    
    // Play ringtone
    window.SallamChat.playNotificationSound('call-ringtone');
    
    // Show call notification
    window.showAlert(`جاري الاتصال ${type === 'voice' ? 'الصوتي' : 'المرئي'} مع ${currentChatUser.fullName}...`, 'info', 3000);
    
    // In a real implementation, this would initiate WebRTC connection
    console.log(`Initiating ${type} call with`, currentChatUser);
}

/**
 * Setup real-time listeners
 */
function setupRealtimeListeners() {
    // Listen for new chats
    const user = window.firebaseAuth.currentUser;
    if (!user) return;
    
    window.firebaseDB
        .collection('chats')
        .where('participants', 'array-contains', user.uid)
        .onSnapshot((snapshot) => {
            snapshot.docChanges().forEach((change) => {
                if (change.type === 'added') {
                    // New chat added
                    console.log('New chat added:', change.doc.id);
                    loadChatList(); // Reload chat list
                }
            });
        });
}

console.log('Chat system loaded successfully');
