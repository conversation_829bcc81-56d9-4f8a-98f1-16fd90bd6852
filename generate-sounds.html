<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Sound Files for SallamChat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: linear-gradient(135deg, #0ea5e9, #10b981);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .sound-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #0ea5e9;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 SallamChat Sound Generator</h1>
        
        <div class="instructions">
            <h3>📋 Instructions</h3>
            <p>This tool generates placeholder sound files for SallamChat. Click the buttons below to generate and download the sound files, then place them in the <code>assets/sounds/</code> directory.</p>
        </div>

        <div class="sound-info">
            <h4>🔔 Message Send Sound</h4>
            <p>A pleasant notification sound played when sending a message.</p>
            <button onclick="generateMessageSound()">Generate & Download</button>
        </div>

        <div class="sound-info">
            <h4>📞 Call Ringtone</h4>
            <p>A ringtone sound played when initiating a call.</p>
            <button onclick="generateRingtone()">Generate & Download</button>
        </div>

        <div class="sound-info">
            <h4>📨 Message Receive Sound</h4>
            <p>A subtle notification sound for incoming messages.</p>
            <button onclick="generateReceiveSound()">Generate & Download</button>
        </div>

        <div id="status" style="margin-top: 20px; padding: 10px; border-radius: 5px; display: none;"></div>
    </div>

    <script>
        // Audio context for generating sounds
        let audioContext;

        function initAudioContext() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
            return audioContext;
        }

        function showStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.style.display = 'block';
            status.textContent = message;
            status.style.background = type === 'success' ? '#d4edda' : '#cce7ff';
            status.style.color = type === 'success' ? '#155724' : '#004085';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function generateTone(frequency, duration, type = 'sine') {
            const ctx = initAudioContext();
            const oscillator = ctx.createOscillator();
            const gainNode = ctx.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(ctx.destination);
            
            oscillator.frequency.setValueAtTime(frequency, ctx.currentTime);
            oscillator.type = type;
            
            gainNode.gain.setValueAtTime(0, ctx.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, ctx.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + duration);
            
            oscillator.start(ctx.currentTime);
            oscillator.stop(ctx.currentTime + duration);
            
            return { oscillator, gainNode };
        }

        function recordAudio(duration) {
            return new Promise((resolve) => {
                const ctx = initAudioContext();
                const dest = ctx.createMediaStreamDestination();
                const mediaRecorder = new MediaRecorder(dest.stream);
                const chunks = [];

                mediaRecorder.ondataavailable = (e) => chunks.push(e.data);
                mediaRecorder.onstop = () => {
                    const blob = new Blob(chunks, { type: 'audio/webm' });
                    resolve(blob);
                };

                mediaRecorder.start();
                
                // Connect audio generation to destination
                const gainNode = ctx.createGain();
                gainNode.connect(dest);
                
                setTimeout(() => {
                    mediaRecorder.stop();
                }, duration * 1000);

                return gainNode;
            });
        }

        async function generateMessageSound() {
            try {
                showStatus('Generating message send sound...', 'info');
                
                const ctx = initAudioContext();
                const dest = ctx.createMediaStreamDestination();
                const mediaRecorder = new MediaRecorder(dest.stream);
                const chunks = [];

                mediaRecorder.ondataavailable = (e) => chunks.push(e.data);
                mediaRecorder.onstop = () => {
                    const blob = new Blob(chunks, { type: 'audio/webm' });
                    downloadBlob(blob, 'message-send.webm');
                    showStatus('Message send sound generated successfully!', 'success');
                };

                mediaRecorder.start();

                // Generate a pleasant notification sound (C major chord)
                const frequencies = [523.25, 659.25, 783.99]; // C5, E5, G5
                frequencies.forEach((freq, index) => {
                    setTimeout(() => {
                        const oscillator = ctx.createOscillator();
                        const gainNode = ctx.createGain();
                        
                        oscillator.connect(gainNode);
                        gainNode.connect(dest);
                        
                        oscillator.frequency.setValueAtTime(freq, ctx.currentTime);
                        oscillator.type = 'sine';
                        
                        gainNode.gain.setValueAtTime(0, ctx.currentTime);
                        gainNode.gain.linearRampToValueAtTime(0.2, ctx.currentTime + 0.01);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3);
                        
                        oscillator.start(ctx.currentTime);
                        oscillator.stop(ctx.currentTime + 0.3);
                    }, index * 50);
                });

                setTimeout(() => {
                    mediaRecorder.stop();
                }, 500);

            } catch (error) {
                showStatus('Error generating sound: ' + error.message, 'error');
            }
        }

        async function generateRingtone() {
            try {
                showStatus('Generating call ringtone...', 'info');
                
                const ctx = initAudioContext();
                const dest = ctx.createMediaStreamDestination();
                const mediaRecorder = new MediaRecorder(dest.stream);
                const chunks = [];

                mediaRecorder.ondataavailable = (e) => chunks.push(e.data);
                mediaRecorder.onstop = () => {
                    const blob = new Blob(chunks, { type: 'audio/webm' });
                    downloadBlob(blob, 'call-ringtone.webm');
                    showStatus('Call ringtone generated successfully!', 'success');
                };

                mediaRecorder.start();

                // Generate a ringtone pattern
                const pattern = [
                    { freq: 440, duration: 0.3 }, // A4
                    { freq: 0, duration: 0.1 },   // Silence
                    { freq: 440, duration: 0.3 }, // A4
                    { freq: 0, duration: 0.1 },   // Silence
                    { freq: 523.25, duration: 0.4 }, // C5
                    { freq: 0, duration: 0.2 },   // Silence
                ];

                let currentTime = 0;
                pattern.forEach(note => {
                    if (note.freq > 0) {
                        setTimeout(() => {
                            const oscillator = ctx.createOscillator();
                            const gainNode = ctx.createGain();
                            
                            oscillator.connect(gainNode);
                            gainNode.connect(dest);
                            
                            oscillator.frequency.setValueAtTime(note.freq, ctx.currentTime);
                            oscillator.type = 'sine';
                            
                            gainNode.gain.setValueAtTime(0, ctx.currentTime);
                            gainNode.gain.linearRampToValueAtTime(0.3, ctx.currentTime + 0.01);
                            gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + note.duration);
                            
                            oscillator.start(ctx.currentTime);
                            oscillator.stop(ctx.currentTime + note.duration);
                        }, currentTime * 1000);
                    }
                    currentTime += note.duration;
                });

                setTimeout(() => {
                    mediaRecorder.stop();
                }, 2000);

            } catch (error) {
                showStatus('Error generating ringtone: ' + error.message, 'error');
            }
        }

        async function generateReceiveSound() {
            try {
                showStatus('Generating message receive sound...', 'info');
                
                const ctx = initAudioContext();
                const dest = ctx.createMediaStreamDestination();
                const mediaRecorder = new MediaRecorder(dest.stream);
                const chunks = [];

                mediaRecorder.ondataavailable = (e) => chunks.push(e.data);
                mediaRecorder.onstop = () => {
                    const blob = new Blob(chunks, { type: 'audio/webm' });
                    downloadBlob(blob, 'message-receive.webm');
                    showStatus('Message receive sound generated successfully!', 'success');
                };

                mediaRecorder.start();

                // Generate a subtle notification sound
                const oscillator = ctx.createOscillator();
                const gainNode = ctx.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(dest);
                
                oscillator.frequency.setValueAtTime(800, ctx.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(600, ctx.currentTime + 0.2);
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0, ctx.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.15, ctx.currentTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2);
                
                oscillator.start(ctx.currentTime);
                oscillator.stop(ctx.currentTime + 0.2);

                setTimeout(() => {
                    mediaRecorder.stop();
                }, 300);

            } catch (error) {
                showStatus('Error generating receive sound: ' + error.message, 'error');
            }
        }

        function downloadBlob(blob, filename) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
