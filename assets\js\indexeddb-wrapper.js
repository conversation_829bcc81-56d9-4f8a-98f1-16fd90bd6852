/**
 * SallamChat - IndexedDB Wrapper
 * Provides a robust browser-based database solution using IndexedDB
 */

class SallamChatDB {
    constructor() {
        this.dbName = 'SallamChatDB';
        this.version = 1;
        this.db = null;
        this.isReady = false;
    }

    /**
     * Initialize the IndexedDB database
     */
    async init() {
        return new Promise((resolve, reject) => {
            if (!window.indexedDB) {
                reject(new Error('IndexedDB is not supported in this browser'));
                return;
            }

            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                reject(new Error('Failed to open IndexedDB'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                this.isReady = true;
                console.log('IndexedDB initialized successfully');
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createObjectStores(db);
            };
        });
    }

    /**
     * Create object stores (tables) for the database
     */
    createObjectStores(db) {
        // Users store
        if (!db.objectStoreNames.contains('users')) {
            const usersStore = db.createObjectStore('users', { keyPath: 'id' });
            usersStore.createIndex('email', 'email', { unique: true });
            usersStore.createIndex('status', 'status', { unique: false });
            usersStore.createIndex('createdAt', 'createdAt', { unique: false });
        }

        // Chats store
        if (!db.objectStoreNames.contains('chats')) {
            const chatsStore = db.createObjectStore('chats', { keyPath: 'id' });
            chatsStore.createIndex('participants', 'participants', { unique: false, multiEntry: true });
            chatsStore.createIndex('lastMessageTime', 'lastMessageTime', { unique: false });
        }

        // Messages store
        if (!db.objectStoreNames.contains('messages')) {
            const messagesStore = db.createObjectStore('messages', { keyPath: 'id' });
            messagesStore.createIndex('chatId', 'chatId', { unique: false });
            messagesStore.createIndex('senderId', 'senderId', { unique: false });
            messagesStore.createIndex('timestamp', 'timestamp', { unique: false });
            messagesStore.createIndex('type', 'type', { unique: false });
        }

        // Settings store
        if (!db.objectStoreNames.contains('settings')) {
            const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
        }

        // Admin store
        if (!db.objectStoreNames.contains('admins')) {
            const adminsStore = db.createObjectStore('admins', { keyPath: 'username' });
        }

        // Files store (for offline file caching)
        if (!db.objectStoreNames.contains('files')) {
            const filesStore = db.createObjectStore('files', { keyPath: 'id' });
            filesStore.createIndex('messageId', 'messageId', { unique: false });
            filesStore.createIndex('type', 'type', { unique: false });
        }

        console.log('IndexedDB object stores created');
    }

    /**
     * Generic method to add data to a store
     */
    async add(storeName, data) {
        if (!this.isReady) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // Generate ID if not provided
            if (!data.id) {
                data.id = this.generateId();
            }
            
            // Add timestamp if not provided
            if (!data.createdAt) {
                data.createdAt = new Date().toISOString();
            }

            const request = store.add(data);

            request.onsuccess = () => {
                resolve(data);
            };

            request.onerror = () => {
                reject(new Error(`Failed to add data to ${storeName}: ${request.error}`));
            };
        });
    }

    /**
     * Generic method to update data in a store
     */
    async update(storeName, data) {
        if (!this.isReady) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // Add update timestamp
            data.updatedAt = new Date().toISOString();

            const request = store.put(data);

            request.onsuccess = () => {
                resolve(data);
            };

            request.onerror = () => {
                reject(new Error(`Failed to update data in ${storeName}: ${request.error}`));
            };
        });
    }

    /**
     * Generic method to get data by ID
     */
    async get(storeName, id) {
        if (!this.isReady) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(new Error(`Failed to get data from ${storeName}: ${request.error}`));
            };
        });
    }

    /**
     * Generic method to get all data from a store
     */
    async getAll(storeName) {
        if (!this.isReady) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(new Error(`Failed to get all data from ${storeName}: ${request.error}`));
            };
        });
    }

    /**
     * Generic method to delete data by ID
     */
    async delete(storeName, id) {
        if (!this.isReady) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);

            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = () => {
                reject(new Error(`Failed to delete data from ${storeName}: ${request.error}`));
            };
        });
    }

    /**
     * Query data using an index
     */
    async queryByIndex(storeName, indexName, value) {
        if (!this.isReady) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(new Error(`Failed to query ${storeName} by ${indexName}: ${request.error}`));
            };
        });
    }

    /**
     * Get data with cursor for pagination
     */
    async getCursor(storeName, indexName = null, direction = 'next', limit = null) {
        if (!this.isReady) await this.init();

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const source = indexName ? store.index(indexName) : store;
            const request = source.openCursor(null, direction);
            
            const results = [];
            let count = 0;

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                
                if (cursor && (!limit || count < limit)) {
                    results.push(cursor.value);
                    count++;
                    cursor.continue();
                } else {
                    resolve(results);
                }
            };

            request.onerror = () => {
                reject(new Error(`Failed to get cursor from ${storeName}: ${request.error}`));
            };
        });
    }

    /**
     * User-specific methods
     */
    async addUser(userData) {
        return this.add('users', userData);
    }

    async getUserByEmail(email) {
        const users = await this.queryByIndex('users', 'email', email);
        return users.length > 0 ? users[0] : null;
    }

    async updateUserStatus(userId, status) {
        const user = await this.get('users', userId);
        if (user) {
            user.status = status;
            user.lastSeen = new Date().toISOString();
            return this.update('users', user);
        }
        return null;
    }

    /**
     * Chat-specific methods
     */
    async addChat(chatData) {
        return this.add('chats', chatData);
    }

    async getChatsByUser(userId) {
        const allChats = await this.getAll('chats');
        return allChats.filter(chat => chat.participants && chat.participants.includes(userId));
    }

    async updateChatLastMessage(chatId, message, senderId) {
        const chat = await this.get('chats', chatId);
        if (chat) {
            chat.lastMessage = message;
            chat.lastMessageTime = new Date().toISOString();
            chat.lastMessageSender = senderId;
            return this.update('chats', chat);
        }
        return null;
    }

    /**
     * Message-specific methods
     */
    async addMessage(messageData) {
        return this.add('messages', messageData);
    }

    async getMessagesByChat(chatId, limit = 50) {
        const messages = await this.queryByIndex('messages', 'chatId', chatId);
        return messages
            .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
            .slice(-limit);
    }

    /**
     * Settings methods
     */
    async setSetting(key, value) {
        return this.update('settings', { key, value });
    }

    async getSetting(key) {
        const setting = await this.get('settings', key);
        return setting ? setting.value : null;
    }

    /**
     * Admin methods
     */
    async addAdmin(adminData) {
        return this.add('admins', adminData);
    }

    async getAdmin(username) {
        return this.get('admins', username);
    }

    /**
     * File methods
     */
    async addFile(fileData) {
        return this.add('files', fileData);
    }

    async getFilesByMessage(messageId) {
        return this.queryByIndex('files', 'messageId', messageId);
    }

    /**
     * Utility methods
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * Clear all data (for testing/reset)
     */
    async clearAllData() {
        if (!this.isReady) await this.init();

        const storeNames = ['users', 'chats', 'messages', 'settings', 'admins', 'files'];
        
        for (const storeName of storeNames) {
            await new Promise((resolve, reject) => {
                const transaction = this.db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);
                const request = store.clear();

                request.onsuccess = () => resolve();
                request.onerror = () => reject(new Error(`Failed to clear ${storeName}`));
            });
        }

        console.log('All IndexedDB data cleared');
    }

    /**
     * Get database statistics
     */
    async getStatistics() {
        const stats = {
            totalUsers: 0,
            totalChats: 0,
            totalMessages: 0,
            onlineUsers: 0
        };

        try {
            const users = await this.getAll('users');
            stats.totalUsers = users.length;
            stats.onlineUsers = users.filter(user => user.status === 'online').length;

            const chats = await this.getAll('chats');
            stats.totalChats = chats.length;

            const messages = await this.getAll('messages');
            stats.totalMessages = messages.length;
        } catch (error) {
            console.error('Error getting statistics:', error);
        }

        return stats;
    }

    /**
     * Export data for backup
     */
    async exportData() {
        const data = {
            users: await this.getAll('users'),
            chats: await this.getAll('chats'),
            messages: await this.getAll('messages'),
            settings: await this.getAll('settings'),
            exportDate: new Date().toISOString()
        };

        return data;
    }

    /**
     * Import data from backup
     */
    async importData(data) {
        if (!data || typeof data !== 'object') {
            throw new Error('Invalid import data');
        }

        // Clear existing data
        await this.clearAllData();

        // Import each store
        const stores = ['users', 'chats', 'messages', 'settings'];
        
        for (const storeName of stores) {
            if (data[storeName] && Array.isArray(data[storeName])) {
                for (const item of data[storeName]) {
                    await this.update(storeName, item);
                }
            }
        }

        console.log('Data imported successfully');
    }
}

// Create global instance
window.sallamChatDB = new SallamChatDB();

// Initialize default admin if needed
window.sallamChatDB.init().then(async () => {
    try {
        const existingAdmin = await window.sallamChatDB.getAdmin('ADMIN');
        if (!existingAdmin) {
            await window.sallamChatDB.addAdmin({
                username: 'ADMIN',
                password: '*#admin1981#*',
                createdAt: new Date().toISOString()
            });
            console.log('Default admin created in IndexedDB');
        }
    } catch (error) {
        console.error('Error setting up default admin:', error);
    }
}).catch(error => {
    console.error('Failed to initialize IndexedDB:', error);
});

console.log('IndexedDB wrapper loaded successfully');
