<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SallamChat - للتواصل الحر والسلمي</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/themes.css" rel="stylesheet">
</head>
<body class="login-page">
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="themeToggle">
        <i class="bi bi-moon-fill" id="themeIcon"></i>
    </button>

    <div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100 justify-content-center">
            <div class="col-md-6 col-lg-4">
                <!-- Logo Section -->
                <div class="text-center mb-4">
                    <div class="logo-container">
                        <div class="logo-circle">
                            <i class="bi bi-chat-dots-fill"></i>
                        </div>
                    </div>
                    <h1 class="app-title mt-3">SallamChat</h1>
                    <p class="app-tagline">للتواصل الحر والسلمي</p>
                </div>

                <!-- Login Form -->
                <div class="auth-card">
                    <div class="card-header text-center">
                        <h3>تسجيل الدخول</h3>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    تذكرني
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                دخول
                            </button>
                        </form>

                        <div class="text-center">
                            <a href="#" class="forgot-password" id="forgotPassword">نسيت كلمة المرور؟</a>
                        </div>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="mb-2">ليس لديك حساب؟</p>
                            <a href="register.html" class="btn btn-outline-primary">
                                <i class="bi bi-person-plus me-2"></i>
                                إنشاء حساب جديد
                            </a>
                        </div>

                        <!-- Admin Login Link -->
                        <div class="text-center mt-4">
                            <a href="admin.html" class="admin-link">
                                <i class="bi bi-gear me-1"></i>
                                دخول المدير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="app-footer">
        <div class="container text-center">
            <p class="mb-1">
                <strong>SallamChat</strong> - للتواصل الحر والسلمي
            </p>
            <p class="mb-0">
                تصميم: م. هاني سلام - جميع الحقوق محفوظة © <span id="currentYear"></span>
            </p>
        </div>
    </footer>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>

    <!-- Alert Container -->
    <div class="alert-container" id="alertContainer"></div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/firebase-config.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>
