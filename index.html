<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SallamChat - للتواصل الحر والسلمي</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/themes.css" rel="stylesheet">
</head>
<body class="login-page">
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="themeToggle">
        <i class="bi bi-moon-fill" id="themeIcon"></i>
    </button>

    <div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center">
        <div class="row w-100 justify-content-center">
            <div class="col-md-6 col-lg-4">
                <!-- Logo Section -->
                <div class="text-center mb-4">
                    <div class="logo-container">
                        <img src="assets/images/logo.png" alt="SallamChat Logo" class="app-logo"
                             onerror="this.src='assets/images/logo-fallback.svg'">
                    </div>
                    <h1 class="app-title mt-3">SallamChat</h1>
                    <p class="app-tagline">للتواصل الحر والسلمي</p>
                </div>

                <!-- Login Form -->
                <div class="auth-card">
                    <div class="card-header text-center">
                        <h3>تسجيل الدخول</h3>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" value="<EMAIL>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" value="123456" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">
                                    تذكرني
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 mb-3" id="loginButton">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                دخول
                            </button>
                        </form>

                        <div class="text-center">
                            <a href="#" class="forgot-password" id="forgotPassword">نسيت كلمة المرور؟</a>
                        </div>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="mb-2">ليس لديك حساب؟</p>
                            <a href="register.html" class="btn btn-outline-primary">
                                <i class="bi bi-person-plus me-2"></i>
                                إنشاء حساب جديد
                            </a>
                        </div>

                        <!-- Admin Login Link -->
                        <div class="text-center mt-4">
                            <a href="admin.html" class="admin-link">
                                <i class="bi bi-gear me-1"></i>
                                دخول المدير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="app-footer">
        <div class="container text-center">
            <p class="mb-1">
                <strong>SallamChat</strong> - للتواصل الحر والسلمي
            </p>
            <p class="mb-0">
                تصميم: م. هاني سلام - جميع الحقوق محفوظة © <span id="currentYear"></span>
            </p>
        </div>
    </footer>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>

    <!-- Alert Container -->
    <div class="alert-container" id="alertContainer"></div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Supabase SDK (ONLY Database) -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Custom JS -->
    <script src="assets/js/supabase-config.js"></script>
    <script src="assets/js/database-config.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/app.js"></script>

    <!-- Simple Direct Login -->
    <script>
        console.log('🔧 Simple direct login loaded');

        // Immediate setup without waiting
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM loaded, setting up simple login');
            setupSimpleLogin();
        });

        function setupSimpleLogin() {
            console.log('🚀 Setting up simple login handler');

            const loginForm = document.getElementById('loginForm');
            const loginButton = document.getElementById('loginButton');

            if (!loginForm) {
                console.error('❌ Login form not found');
                return;
            }

            console.log('✅ Login form found');

            // Remove any existing event listeners
            const newForm = loginForm.cloneNode(true);
            loginForm.parentNode.replaceChild(newForm, loginForm);

            const newButton = document.getElementById('loginButton');

            // Add simple click handler
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔘 Login button clicked');
                performSimpleLogin();
            });

            // Add form submit handler
            newForm.addEventListener('submit', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('📝 Form submitted');
                performSimpleLogin();
            });

            console.log('✅ Event handlers added');
        }

        function performSimpleLogin() {
            console.log('🔐 Starting simple login');

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            console.log('📧 Email:', email);
            console.log('🔒 Password length:', password.length);

            if (!email || !password) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            // Validate test credentials
            const validCredentials = [
                { email: '<EMAIL>', password: '123456' },
                { email: '<EMAIL>', password: '123456' },
                { email: '<EMAIL>', password: '123456' },
                { email: '<EMAIL>', password: '123456' },
                { email: '<EMAIL>', password: '123456' }
            ];

            const isValid = validCredentials.some(cred =>
                cred.email === email && cred.password === password
            );

            if (!isValid) {
                alert('البريد الإلكتروني أو كلمة المرور غير صحيحة\nاستخدم: <EMAIL> / 123456');
                return;
            }

            console.log('✅ Credentials valid');

            // Show loading
            const loginButton = document.getElementById('loginButton');
            if (loginButton) {
                loginButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الدخول...';
                loginButton.disabled = true;
            }

            // Save user session immediately
            const userData = {
                uid: 'user_' + Date.now(),
                email: email,
                displayName: email.split('@')[0],
                loginTime: new Date().toISOString(),
                isLoggedIn: true
            };

            localStorage.setItem('sallamchat_current_user', JSON.stringify(userData));
            localStorage.setItem('sallamchat_logged_in', 'true');

            console.log('💾 User session saved:', userData);

            // Show success
            if (loginButton) {
                loginButton.innerHTML = '<i class="bi bi-check-circle me-2"></i>تم بنجاح!';
                loginButton.className = 'btn btn-success w-100 mb-3';
            }

            console.log('🚀 Redirecting to chat...');

            // Immediate redirect
            setTimeout(() => {
                window.location.href = 'chat.html';
            }, 500);
        }

        // Add quick test button
        setTimeout(() => {
            const quickTestBtn = document.createElement('button');
            quickTestBtn.innerHTML = '🚀 دخول سريع (أحمد)';
            quickTestBtn.className = 'btn btn-warning w-100 mt-2';
            quickTestBtn.onclick = function() {
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('password').value = '123456';
                performSimpleLogin();
            };

            const form = document.getElementById('loginForm');
            if (form) {
                form.appendChild(quickTestBtn);
            }
        }, 1000);
    </script>
</body>
</html>
