/**
 * SallamChat - Admin Panel JavaScript
 * Handles admin authentication, user management, and statistics
 */

// Global admin variables
let currentAdmin = null;
let usersData = [];
let statisticsData = {
    totalUsers: 0,
    totalMessages: 0,
    totalCalls: 0,
    onlineUsers: 0
};

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

/**
 * Initialize admin panel
 */
function initializeAdmin() {
    console.log('Initializing admin panel');
    
    // Initialize admin login form
    const adminLoginForm = document.getElementById('adminLoginForm');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', handleAdminLogin);
    }
    
    // Initialize admin logout button
    const adminLogoutBtn = document.getElementById('adminLogoutBtn');
    if (adminLogoutBtn) {
        adminLogoutBtn.addEventListener('click', handleAdminLogout);
    }
    
    // Initialize user search
    const searchUsers = document.getElementById('searchUsers');
    if (searchUsers) {
        searchUsers.addEventListener('input', filterUsers);
    }
    
    // Initialize save user changes button
    const saveUserChanges = document.getElementById('saveUserChanges');
    if (saveUserChanges) {
        saveUserChanges.addEventListener('click', saveUserChanges);
    }
    
    // Check if admin is already logged in
    checkAdminAuth();
}

/**
 * Handle admin login
 */
async function handleAdminLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('adminUsername').value.trim();
    const password = document.getElementById('adminPassword').value;
    
    // Validate inputs
    if (!username || !password) {
        window.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    try {
        window.showLoading();
        
        // Check admin credentials
        const isValidAdmin = await validateAdminCredentials(username, password);
        
        if (isValidAdmin) {
            currentAdmin = { username: username };
            
            // Save admin session
            localStorage.setItem('sallamchat_admin', JSON.stringify(currentAdmin));
            
            // Show admin dashboard
            showAdminDashboard();
            
            // Load admin data
            await loadAdminData();
            
            window.showAlert('تم تسجيل دخول المدير بنجاح!', 'success', 2000);
            
        } else {
            window.showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger');
        }
        
    } catch (error) {
        console.error('Admin login error:', error);
        window.showAlert('حدث خطأ أثناء تسجيل الدخول', 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Validate admin credentials
 */
async function validateAdminCredentials(username, password) {
    try {
        if (window.mockFirebase) {
            // Use localStorage for mock mode
            const admins = JSON.parse(localStorage.getItem('sallamchat_admins') || '[]');
            return admins.some(admin => admin.username === username && admin.password === password);
        } else {
            // Use Firebase for production
            const adminDoc = await window.firebaseDB.collection('admins').doc(username).get();
            if (adminDoc.exists) {
                const adminData = adminDoc.data();
                return adminData.password === password;
            }
        }
        return false;
    } catch (error) {
        console.error('Error validating admin credentials:', error);
        return false;
    }
}

/**
 * Handle admin logout
 */
function handleAdminLogout() {
    currentAdmin = null;
    localStorage.removeItem('sallamchat_admin');
    
    // Show login screen
    showAdminLogin();
    
    window.showAlert('تم تسجيل خروج المدير بنجاح', 'success', 2000);
}

/**
 * Check admin authentication
 */
function checkAdminAuth() {
    const savedAdmin = localStorage.getItem('sallamchat_admin');
    if (savedAdmin) {
        try {
            currentAdmin = JSON.parse(savedAdmin);
            showAdminDashboard();
            loadAdminData();
        } catch (error) {
            console.error('Error parsing saved admin data:', error);
            localStorage.removeItem('sallamchat_admin');
        }
    }
}

/**
 * Show admin login screen
 */
function showAdminLogin() {
    const loginScreen = document.getElementById('adminLoginScreen');
    const dashboard = document.getElementById('adminDashboard');
    
    if (loginScreen) loginScreen.classList.remove('d-none');
    if (dashboard) dashboard.classList.add('d-none');
}

/**
 * Show admin dashboard
 */
function showAdminDashboard() {
    const loginScreen = document.getElementById('adminLoginScreen');
    const dashboard = document.getElementById('adminDashboard');
    
    if (loginScreen) loginScreen.classList.add('d-none');
    if (dashboard) dashboard.classList.remove('d-none');
}

/**
 * Load admin data
 */
async function loadAdminData() {
    try {
        await Promise.all([
            loadStatistics(),
            loadUsers(),
            loadRecentActivity()
        ]);
    } catch (error) {
        console.error('Error loading admin data:', error);
        window.showAlert('حدث خطأ في تحميل بيانات الإدارة', 'danger');
    }
}

/**
 * Load statistics
 */
async function loadStatistics() {
    try {
        if (window.mockFirebase) {
            // Mock statistics for demo
            statisticsData = {
                totalUsers: Math.floor(Math.random() * 100) + 50,
                totalMessages: Math.floor(Math.random() * 1000) + 500,
                totalCalls: Math.floor(Math.random() * 50) + 25,
                onlineUsers: Math.floor(Math.random() * 20) + 5
            };
        } else {
            // Get real statistics from Firebase
            const usersSnapshot = await window.firebaseDB.collection('users').get();
            statisticsData.totalUsers = usersSnapshot.size;
            
            // Count online users
            const onlineUsersSnapshot = await window.firebaseDB
                .collection('users')
                .where('status', '==', 'online')
                .get();
            statisticsData.onlineUsers = onlineUsersSnapshot.size;
            
            // Count total messages (this would be expensive in production, consider using counters)
            let totalMessages = 0;
            const chatsSnapshot = await window.firebaseDB.collection('chats').get();
            for (const chatDoc of chatsSnapshot.docs) {
                const messagesSnapshot = await chatDoc.ref.collection('messages').get();
                totalMessages += messagesSnapshot.size;
            }
            statisticsData.totalMessages = totalMessages;
            
            // Mock calls data (implement call tracking in production)
            statisticsData.totalCalls = Math.floor(totalMessages * 0.1);
        }
        
        // Update UI
        updateStatisticsUI();
        
    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

/**
 * Update statistics UI
 */
function updateStatisticsUI() {
    const elements = {
        totalUsers: document.getElementById('totalUsers'),
        totalMessages: document.getElementById('totalMessages'),
        totalCalls: document.getElementById('totalCalls'),
        onlineUsers: document.getElementById('onlineUsers')
    };
    
    Object.keys(elements).forEach(key => {
        if (elements[key]) {
            elements[key].textContent = statisticsData[key].toLocaleString('ar-SA');
        }
    });
}

/**
 * Load users data
 */
async function loadUsers() {
    try {
        usersData = [];
        
        if (window.mockFirebase) {
            // Load from localStorage
            const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
            usersData = users.map(user => ({
                id: user.id,
                fullName: user.fullName || 'مستخدم',
                email: user.email,
                createdAt: user.createdAt,
                status: user.status || 'active'
            }));
        } else {
            // Load from Firebase
            const usersSnapshot = await window.firebaseDB.collection('users').get();
            usersData = usersSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        }
        
        // Update users table
        updateUsersTable();
        
    } catch (error) {
        console.error('Error loading users:', error);
    }
}

/**
 * Update users table
 */
function updateUsersTable() {
    const tableBody = document.getElementById('usersTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    usersData.forEach(user => {
        const row = document.createElement('tr');
        
        const statusBadge = getStatusBadge(user.status);
        const createdDate = user.createdAt ? 
            new Date(user.createdAt).toLocaleDateString('ar-SA') : 'غير محدد';
        
        row.innerHTML = `
            <td>${window.SallamChat.sanitizeHTML(user.fullName || 'غير محدد')}</td>
            <td>${window.SallamChat.sanitizeHTML(user.email)}</td>
            <td>${createdDate}</td>
            <td>${statusBadge}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-2" onclick="editUser('${user.id}')">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.id}')">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
    });
}

/**
 * Get status badge HTML
 */
function getStatusBadge(status) {
    const badges = {
        'active': '<span class="badge bg-success">نشط</span>',
        'suspended': '<span class="badge bg-warning">معلق</span>',
        'banned': '<span class="badge bg-danger">محظور</span>',
        'online': '<span class="badge bg-success">متصل</span>',
        'offline': '<span class="badge bg-secondary">غير متصل</span>'
    };
    
    return badges[status] || '<span class="badge bg-secondary">غير محدد</span>';
}

/**
 * Filter users based on search
 */
function filterUsers() {
    const searchTerm = document.getElementById('searchUsers').value.toLowerCase();
    const rows = document.querySelectorAll('#usersTableBody tr');
    
    rows.forEach(row => {
        const name = row.cells[0].textContent.toLowerCase();
        const email = row.cells[1].textContent.toLowerCase();
        
        if (name.includes(searchTerm) || email.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * Edit user
 */
function editUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    // Populate edit form
    document.getElementById('editUserId').value = userId;
    document.getElementById('editUserName').value = user.fullName || '';
    document.getElementById('editUserEmail').value = user.email;
    document.getElementById('editUserStatus').value = user.status || 'active';
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
    modal.show();
}

/**
 * Save user changes
 */
async function saveUserChanges() {
    const userId = document.getElementById('editUserId').value;
    const fullName = document.getElementById('editUserName').value.trim();
    const email = document.getElementById('editUserEmail').value.trim();
    const status = document.getElementById('editUserStatus').value;
    
    if (!fullName || !email) {
        window.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }
    
    try {
        window.showLoading();
        
        const updateData = {
            fullName: fullName,
            email: email,
            status: status,
            updatedAt: new Date().toISOString()
        };
        
        if (window.mockFirebase) {
            // Update in localStorage
            const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
            const userIndex = users.findIndex(u => u.id === userId);
            if (userIndex !== -1) {
                users[userIndex] = { ...users[userIndex], ...updateData };
                localStorage.setItem('sallamchat_users', JSON.stringify(users));
            }
        } else {
            // Update in Firebase
            await window.firebaseDB.collection('users').doc(userId).update(updateData);
        }
        
        // Reload users data
        await loadUsers();
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
        modal.hide();
        
        window.showAlert('تم تحديث بيانات المستخدم بنجاح', 'success');
        
    } catch (error) {
        console.error('Error updating user:', error);
        window.showAlert('حدث خطأ في تحديث بيانات المستخدم', 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Delete user
 */
async function deleteUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;
    
    const confirmed = confirm(`هل أنت متأكد من حذف المستخدم "${user.fullName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmed) return;
    
    try {
        window.showLoading();
        
        if (window.mockFirebase) {
            // Delete from localStorage
            const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
            const filteredUsers = users.filter(u => u.id !== userId);
            localStorage.setItem('sallamchat_users', JSON.stringify(filteredUsers));
        } else {
            // Delete from Firebase
            await window.firebaseDB.collection('users').doc(userId).delete();
        }
        
        // Reload users data
        await loadUsers();
        await loadStatistics();
        
        window.showAlert('تم حذف المستخدم بنجاح', 'success');
        
    } catch (error) {
        console.error('Error deleting user:', error);
        window.showAlert('حدث خطأ في حذف المستخدم', 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Load recent activity
 */
async function loadRecentActivity() {
    try {
        const recentActivity = document.getElementById('recentActivity');
        if (!recentActivity) return;
        
        // Mock recent activity data
        const activities = [
            { type: 'user_registered', user: 'أحمد محمد', time: 'منذ 5 دقائق' },
            { type: 'message_sent', user: 'فاطمة علي', time: 'منذ 10 دقائق' },
            { type: 'user_login', user: 'محمد أحمد', time: 'منذ 15 دقيقة' },
            { type: 'call_made', user: 'سارة محمود', time: 'منذ 20 دقيقة' },
            { type: 'user_logout', user: 'علي حسن', time: 'منذ 25 دقيقة' }
        ];
        
        recentActivity.innerHTML = activities.map(activity => {
            const icon = getActivityIcon(activity.type);
            const text = getActivityText(activity.type, activity.user);
            
            return `
                <div class="d-flex align-items-center mb-3">
                    <div class="activity-icon me-3">
                        <i class="bi ${icon}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-0">${text}</p>
                        <small class="text-muted">${activity.time}</small>
                    </div>
                </div>
            `;
        }).join('');
        
    } catch (error) {
        console.error('Error loading recent activity:', error);
    }
}

/**
 * Get activity icon
 */
function getActivityIcon(type) {
    const icons = {
        'user_registered': 'bi-person-plus',
        'message_sent': 'bi-chat-dots',
        'user_login': 'bi-box-arrow-in-right',
        'call_made': 'bi-telephone',
        'user_logout': 'bi-box-arrow-right'
    };
    
    return icons[type] || 'bi-info-circle';
}

/**
 * Get activity text
 */
function getActivityText(type, user) {
    const texts = {
        'user_registered': `انضم ${user} إلى التطبيق`,
        'message_sent': `أرسل ${user} رسالة`,
        'user_login': `سجل ${user} دخوله`,
        'call_made': `أجرى ${user} مكالمة`,
        'user_logout': `سجل ${user} خروجه`
    };
    
    return texts[type] || `نشاط من ${user}`;
}

// Make functions globally available
window.editUser = editUser;
window.deleteUser = deleteUser;
window.saveUserChanges = saveUserChanges;

console.log('Admin panel loaded successfully');
