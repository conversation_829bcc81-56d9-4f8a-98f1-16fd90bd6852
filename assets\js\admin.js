/**
 * SallamChat - Admin Panel JavaScript
 * Handles admin authentication, user management, and statistics
 */

// Global admin variables
let currentAdmin = null;
let usersData = [];
let statisticsData = {
    totalUsers: 0,
    totalMessages: 0,
    totalCalls: 0,
    onlineUsers: 0
};

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

/**
 * Initialize admin panel
 */
function initializeAdmin() {
    console.log('Initializing admin panel');

    // Initialize admin login form
    const adminLoginForm = document.getElementById('adminLoginForm');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', handleAdminLogin);
    }

    // Initialize admin logout button
    const adminLogoutBtn = document.getElementById('adminLogoutBtn');
    if (adminLogoutBtn) {
        adminLogoutBtn.addEventListener('click', handleAdminLogout);
    }

    // Initialize user search
    const searchUsers = document.getElementById('searchUsers');
    if (searchUsers) {
        searchUsers.addEventListener('input', filterUsers);
    }

    // Initialize save user changes button
    const saveUserChanges = document.getElementById('saveUserChanges');
    if (saveUserChanges) {
        saveUserChanges.addEventListener('click', saveUserChanges);
    }

    // Check if admin is already logged in
    checkAdminAuth();
}

/**
 * Handle admin login
 */
async function handleAdminLogin(event) {
    event.preventDefault();

    const username = document.getElementById('adminUsername').value.trim();
    const password = document.getElementById('adminPassword').value;

    // Validate inputs
    if (!username || !password) {
        window.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    try {
        window.showLoading();

        // Check admin credentials
        const isValidAdmin = await validateAdminCredentials(username, password);

        if (isValidAdmin) {
            currentAdmin = { username: username };

            // Save admin session
            localStorage.setItem('sallamchat_admin', JSON.stringify(currentAdmin));

            // Show admin dashboard
            showAdminDashboard();

            // Load admin data
            await loadAdminData();

            window.showAlert('تم تسجيل دخول المدير بنجاح!', 'success', 2000);

        } else {
            window.showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger');
        }

    } catch (error) {
        console.error('Admin login error:', error);
        window.showAlert('حدث خطأ أثناء تسجيل الدخول', 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Validate admin credentials
 */
async function validateAdminCredentials(username, password) {
    try {
        // SallamChat uses Supabase ONLY
        if (window.useSupabase && window.supabaseClient) {
            const { data, error } = await window.supabaseClient
                .from('admins')
                .select('*')
                .eq('username', username)
                .single();

            if (error && error.code !== 'PGRST116') {
                throw new Error(error.message);
            }

            return data && data.password_hash === password;
        } else {
            throw new Error('Supabase is required for admin authentication');
        }
    } catch (error) {
        console.error('Error validating admin credentials:', error);
        return false;
    }
}

/**
 * Handle admin logout
 */
function handleAdminLogout() {
    currentAdmin = null;
    localStorage.removeItem('sallamchat_admin');

    // Show login screen
    showAdminLogin();

    window.showAlert('تم تسجيل خروج المدير بنجاح', 'success', 2000);
}

/**
 * Check admin authentication
 */
function checkAdminAuth() {
    const savedAdmin = localStorage.getItem('sallamchat_admin');
    if (savedAdmin) {
        try {
            currentAdmin = JSON.parse(savedAdmin);
            showAdminDashboard();
            loadAdminData();
        } catch (error) {
            console.error('Error parsing saved admin data:', error);
            localStorage.removeItem('sallamchat_admin');
        }
    }
}

/**
 * Show admin login screen
 */
function showAdminLogin() {
    const loginScreen = document.getElementById('adminLoginScreen');
    const dashboard = document.getElementById('adminDashboard');

    if (loginScreen) loginScreen.classList.remove('d-none');
    if (dashboard) dashboard.classList.add('d-none');
}

/**
 * Show admin dashboard
 */
function showAdminDashboard() {
    const loginScreen = document.getElementById('adminLoginScreen');
    const dashboard = document.getElementById('adminDashboard');

    if (loginScreen) loginScreen.classList.add('d-none');
    if (dashboard) dashboard.classList.remove('d-none');
}

/**
 * Load admin data
 */
async function loadAdminData() {
    try {
        await Promise.all([
            loadStatistics(),
            loadUsers(),
            loadRecentActivity()
        ]);
    } catch (error) {
        console.error('Error loading admin data:', error);
        window.showAlert('حدث خطأ في تحميل بيانات الإدارة', 'danger');
    }
}

/**
 * Load statistics
 */
async function loadStatistics() {
    try {
        // SallamChat uses Supabase ONLY
        if (window.useSupabase && window.supabaseClient) {
            const { data: users, error: usersError } = await window.supabaseClient
                .from('users')
                .select('id, status');

            if (usersError) throw new Error(usersError.message);

            const { data: messages, error: messagesError } = await window.supabaseClient
                .from('messages')
                .select('id');

            if (messagesError) throw new Error(messagesError.message);

            statisticsData = {
                totalUsers: users ? users.length : 0,
                totalMessages: messages ? messages.length : 0,
                onlineUsers: users ? users.filter(user => user.status === 'online').length : 0,
                totalCalls: messages ? Math.floor(messages.length * 0.1) : 0
            };
        } else {
            throw new Error('Supabase is required for statistics');
        }

        // Update UI
        updateStatisticsUI();

    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

/**
 * Update statistics UI
 */
function updateStatisticsUI() {
    const elements = {
        totalUsers: document.getElementById('totalUsers'),
        totalMessages: document.getElementById('totalMessages'),
        totalCalls: document.getElementById('totalCalls'),
        onlineUsers: document.getElementById('onlineUsers')
    };

    Object.keys(elements).forEach(key => {
        if (elements[key]) {
            elements[key].textContent = statisticsData[key].toLocaleString('ar-SA');
        }
    });
}

/**
 * Load users data
 */
async function loadUsers() {
    try {
        usersData = [];

        if (window.sallamChatDB && window.sallamChatDB.isReady) {
            // Load from IndexedDB
            const users = await window.sallamChatDB.getAll('users');
            usersData = users.map(user => ({
                id: user.id,
                fullName: user.fullName || 'مستخدم',
                email: user.email,
                createdAt: user.createdAt,
                status: user.status || 'active'
            }));
        } else if (window.mockFirebase) {
            // Load from localStorage
            const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
            usersData = users.map(user => ({
                id: user.id,
                fullName: user.fullName || 'مستخدم',
                email: user.email,
                createdAt: user.createdAt,
                status: user.status || 'active'
            }));
        } else {
            // Load from Firebase
            const usersSnapshot = await window.firebaseDB.collection('users').get();
            usersData = usersSnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            }));
        }

        // Update users table
        updateUsersTable();

    } catch (error) {
        console.error('Error loading users:', error);
    }
}

/**
 * Update users table
 */
function updateUsersTable() {
    const tableBody = document.getElementById('usersTableBody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    usersData.forEach(user => {
        const row = document.createElement('tr');

        const statusBadge = getStatusBadge(user.status);
        const createdDate = user.createdAt ?
            new Date(user.createdAt).toLocaleDateString('ar-SA') : 'غير محدد';

        row.innerHTML = `
            <td>${window.SallamChat.sanitizeHTML(user.fullName || 'غير محدد')}</td>
            <td>${window.SallamChat.sanitizeHTML(user.email)}</td>
            <td>${createdDate}</td>
            <td>${statusBadge}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-2" onclick="editUser('${user.id}')">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.id}')">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });
}

/**
 * Get status badge HTML
 */
function getStatusBadge(status) {
    const badges = {
        'active': '<span class="badge bg-success">نشط</span>',
        'suspended': '<span class="badge bg-warning">معلق</span>',
        'banned': '<span class="badge bg-danger">محظور</span>',
        'online': '<span class="badge bg-success">متصل</span>',
        'offline': '<span class="badge bg-secondary">غير متصل</span>'
    };

    return badges[status] || '<span class="badge bg-secondary">غير محدد</span>';
}

/**
 * Filter users based on search
 */
function filterUsers() {
    const searchTerm = document.getElementById('searchUsers').value.toLowerCase();
    const rows = document.querySelectorAll('#usersTableBody tr');

    rows.forEach(row => {
        const name = row.cells[0].textContent.toLowerCase();
        const email = row.cells[1].textContent.toLowerCase();

        if (name.includes(searchTerm) || email.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * Edit user
 */
function editUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;

    // Populate edit form
    document.getElementById('editUserId').value = userId;
    document.getElementById('editUserName').value = user.fullName || '';
    document.getElementById('editUserEmail').value = user.email;
    document.getElementById('editUserStatus').value = user.status || 'active';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
    modal.show();
}

/**
 * Save user changes
 */
async function saveUserChanges() {
    const userId = document.getElementById('editUserId').value;
    const fullName = document.getElementById('editUserName').value.trim();
    const email = document.getElementById('editUserEmail').value.trim();
    const status = document.getElementById('editUserStatus').value;

    if (!fullName || !email) {
        window.showAlert('يرجى ملء جميع الحقول المطلوبة', 'warning');
        return;
    }

    try {
        window.showLoading();

        const updateData = {
            fullName: fullName,
            email: email,
            status: status,
            updatedAt: new Date().toISOString()
        };

        if (window.sallamChatDB && window.sallamChatDB.isReady) {
            // Update in IndexedDB
            const existingUser = await window.sallamChatDB.get('users', userId);
            if (existingUser) {
                const updatedUser = { ...existingUser, ...updateData };
                await window.sallamChatDB.update('users', updatedUser);
            }
        } else if (window.mockFirebase) {
            // Update in localStorage
            const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
            const userIndex = users.findIndex(u => u.id === userId);
            if (userIndex !== -1) {
                users[userIndex] = { ...users[userIndex], ...updateData };
                localStorage.setItem('sallamchat_users', JSON.stringify(users));
            }
        } else {
            // Update in Firebase
            await window.firebaseDB.collection('users').doc(userId).update(updateData);
        }

        // Reload users data
        await loadUsers();

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
        modal.hide();

        window.showAlert('تم تحديث بيانات المستخدم بنجاح', 'success');

    } catch (error) {
        console.error('Error updating user:', error);
        window.showAlert('حدث خطأ في تحديث بيانات المستخدم', 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Delete user
 */
async function deleteUser(userId) {
    const user = usersData.find(u => u.id === userId);
    if (!user) return;

    const confirmed = confirm(`هل أنت متأكد من حذف المستخدم "${user.fullName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`);
    if (!confirmed) return;

    try {
        window.showLoading();

        if (window.sallamChatDB && window.sallamChatDB.isReady) {
            // Delete from IndexedDB
            await window.sallamChatDB.delete('users', userId);
        } else if (window.mockFirebase) {
            // Delete from localStorage
            const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
            const filteredUsers = users.filter(u => u.id !== userId);
            localStorage.setItem('sallamchat_users', JSON.stringify(filteredUsers));
        } else {
            // Delete from Firebase
            await window.firebaseDB.collection('users').doc(userId).delete();
        }

        // Reload users data
        await loadUsers();
        await loadStatistics();

        window.showAlert('تم حذف المستخدم بنجاح', 'success');

    } catch (error) {
        console.error('Error deleting user:', error);
        window.showAlert('حدث خطأ في حذف المستخدم', 'danger');
    } finally {
        window.hideLoading();
    }
}

/**
 * Load recent activity
 */
async function loadRecentActivity() {
    try {
        const recentActivity = document.getElementById('recentActivity');
        if (!recentActivity) return;

        // Mock recent activity data
        const activities = [
            { type: 'user_registered', user: 'أحمد محمد', time: 'منذ 5 دقائق' },
            { type: 'message_sent', user: 'فاطمة علي', time: 'منذ 10 دقائق' },
            { type: 'user_login', user: 'محمد أحمد', time: 'منذ 15 دقيقة' },
            { type: 'call_made', user: 'سارة محمود', time: 'منذ 20 دقيقة' },
            { type: 'user_logout', user: 'علي حسن', time: 'منذ 25 دقيقة' }
        ];

        recentActivity.innerHTML = activities.map(activity => {
            const icon = getActivityIcon(activity.type);
            const text = getActivityText(activity.type, activity.user);

            return `
                <div class="d-flex align-items-center mb-3">
                    <div class="activity-icon me-3">
                        <i class="bi ${icon}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="mb-0">${text}</p>
                        <small class="text-muted">${activity.time}</small>
                    </div>
                </div>
            `;
        }).join('');

    } catch (error) {
        console.error('Error loading recent activity:', error);
    }
}

/**
 * Get activity icon
 */
function getActivityIcon(type) {
    const icons = {
        'user_registered': 'bi-person-plus',
        'message_sent': 'bi-chat-dots',
        'user_login': 'bi-box-arrow-in-right',
        'call_made': 'bi-telephone',
        'user_logout': 'bi-box-arrow-right'
    };

    return icons[type] || 'bi-info-circle';
}

/**
 * Get activity text
 */
function getActivityText(type, user) {
    const texts = {
        'user_registered': `انضم ${user} إلى التطبيق`,
        'message_sent': `أرسل ${user} رسالة`,
        'user_login': `سجل ${user} دخوله`,
        'call_made': `أجرى ${user} مكالمة`,
        'user_logout': `سجل ${user} خروجه`
    };

    return texts[type] || `نشاط من ${user}`;
}

/**
 * Email functionality
 */

// Email configuration presets
const emailPresets = {
    gmail: {
        host: 'smtp.gmail.com',
        port: 587,
        security: 'tls'
    },
    outlook: {
        host: 'smtp-mail.outlook.com',
        port: 587,
        security: 'tls'
    },
    yahoo: {
        host: 'smtp.mail.yahoo.com',
        port: 587,
        security: 'tls'
    }
};

// Global email variables
let emailRecipients = [];
let emailConfig = {};

/**
 * Show email modal
 */
function showEmailModal() {
    // Load saved email configuration
    loadEmailConfig();

    // Update recipients count
    updateRecipients();

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('emailModal'));
    modal.show();
}

/**
 * Update email configuration based on service selection
 */
function updateEmailConfig() {
    const service = document.getElementById('emailService').value;
    const smtpConfig = document.getElementById('smtpConfig');

    if (service === 'smtp') {
        smtpConfig.style.display = 'block';
    } else {
        smtpConfig.style.display = 'none';

        // Auto-fill configuration for known services
        if (emailPresets[service]) {
            document.getElementById('smtpHost').value = emailPresets[service].host;
            document.getElementById('smtpPort').value = emailPresets[service].port;
            document.getElementById('smtpSecurity').value = emailPresets[service].security;
        }
    }
}

/**
 * Update recipients based on selection
 */
async function updateRecipients() {
    const recipientType = document.querySelector('input[name="recipientType"]:checked').value;
    const customEmailsDiv = document.getElementById('customEmailsDiv');
    const recipientCountText = document.getElementById('recipientCountText');

    try {
        if (recipientType === 'custom') {
            customEmailsDiv.style.display = 'block';
            recipientCountText.textContent = 'سيتم تحديد العدد بناءً على العناوين المدخلة';
        } else {
            customEmailsDiv.style.display = 'none';

            // Get users from Supabase
            if (window.useSupabase && window.supabaseClient) {
                let query = window.supabaseClient.from('users').select('email, status');

                if (recipientType === 'active') {
                    query = query.eq('status', 'active');
                }

                const { data: users, error } = await query;

                if (error) {
                    throw new Error(error.message);
                }

                emailRecipients = users.map(user => user.email).filter(email => email);
                recipientCountText.textContent = `${emailRecipients.length} مستقبل`;
            } else {
                recipientCountText.textContent = 'خطأ: Supabase غير متاح';
            }
        }
    } catch (error) {
        console.error('Error updating recipients:', error);
        recipientCountText.textContent = 'خطأ في تحميل المستقبلين';
    }
}

/**
 * Preview email content
 */
function previewEmail() {
    const subject = document.getElementById('emailSubject').value;
    const body = document.getElementById('emailBody').value;
    const isHtml = document.getElementById('emailHtml').checked;

    const previewDiv = document.getElementById('emailPreview');
    const previewContent = document.getElementById('previewContent');

    if (!subject || !body) {
        alert('يرجى ملء موضوع الرسالة والمحتوى أولاً');
        return;
    }

    let content = `<strong>الموضوع:</strong> ${subject}<br><br>`;

    if (isHtml) {
        content += `<strong>المحتوى (HTML):</strong><br><div style="border: 1px solid #ddd; padding: 10px; margin-top: 10px;">${body}</div>`;
    } else {
        content += `<strong>المحتوى (نص عادي):</strong><br><pre style="white-space: pre-wrap; background: #f8f9fa; padding: 10px; border-radius: 5px;">${body}</pre>`;
    }

    previewContent.innerHTML = content;
    previewDiv.style.display = 'block';
}

/**
 * Test email connection
 */
async function testEmailConnection(event) {
    const config = getEmailConfig();

    if (!validateEmailConfig(config)) {
        return;
    }

    const button = event ? event.target : document.querySelector('[onclick="testEmailConnection()"]');

    try {
        // Show loading
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الاختبار...';
        button.disabled = true;

        // Initialize email service
        await window.emailService.initialize(config);

        // Test connection
        await window.emailService.testConnection();

        window.showAlert('تم اختبار الاتصال بنجاح! ✅', 'success');

        // Restore button
        button.innerHTML = originalText;
        button.disabled = false;

    } catch (error) {
        console.error('Email connection test failed:', error);
        window.showAlert('فشل في اختبار الاتصال: ' + error.message, 'danger');

        // Restore button
        button.innerHTML = '<i class="bi bi-wifi me-2"></i>اختبار الاتصال';
        button.disabled = false;
    }
}

/**
 * Send emails to recipients
 */
async function sendEmails() {
    const config = getEmailConfig();

    if (!validateEmailConfig(config)) {
        return;
    }

    // Get recipients
    const recipientType = document.querySelector('input[name="recipientType"]:checked').value;
    let recipients = [];

    if (recipientType === 'custom') {
        const customEmails = document.getElementById('customEmailList').value;
        recipients = parseEmailList(customEmails);

        if (recipients.length === 0) {
            window.showAlert('يرجى إدخال عناوين بريد إلكتروني صحيحة', 'warning');
            return;
        }
    } else {
        recipients = emailRecipients;
    }

    if (recipients.length === 0) {
        window.showAlert('لا توجد عناوين بريد إلكتروني للإرسال إليها', 'warning');
        return;
    }

    // Confirm sending
    const confirmed = confirm(`هل أنت متأكد من إرسال الرسالة إلى ${recipients.length} مستقبل؟`);
    if (!confirmed) return;

    // Hide email modal and show progress modal
    const emailModal = bootstrap.Modal.getInstance(document.getElementById('emailModal'));
    emailModal.hide();

    const progressModal = new bootstrap.Modal(document.getElementById('emailProgressModal'));
    progressModal.show();

    // Save configuration if requested
    if (document.getElementById('emailSaveConfig').checked) {
        saveEmailConfig(config);
    }

    // Start sending emails
    await sendEmailBatch(recipients, config);
}

/**
 * Send batch of emails
 */
async function sendEmailBatch(recipients, config) {
    const progressBar = document.getElementById('emailProgress');
    const statusDiv = document.getElementById('emailStatus');
    const resultsDiv = document.getElementById('emailResults');

    try {
        // Initialize email service
        await window.emailService.initialize(config);

        statusDiv.textContent = 'جاري تحضير خدمة البريد الإلكتروني...';

        // Prepare email content
        const emailContent = config.isHtml ?
            window.emailService.createEmailTemplate(config.body, true) :
            config.body;

        // Send emails with progress tracking
        const results = await window.emailService.sendBulkEmails(
            recipients,
            config.subject,
            emailContent,
            config.isHtml,
            (progress) => {
                // Update progress bar
                const percentage = (progress.current / progress.total) * 100;
                progressBar.style.width = percentage + '%';

                // Update status
                statusDiv.textContent = `جاري إرسال الرسالة ${progress.current} من ${progress.total} إلى ${progress.recipient}`;

                // Add result to display
                if (progress.status === 'success') {
                    const provider = window.emailService.getEmailProvider(progress.recipient);
                    resultsDiv.innerHTML += `<div class="text-success small">✅ تم إرسال الرسالة إلى ${progress.recipient} (${provider})</div>`;
                } else if (progress.status === 'failed') {
                    const provider = window.emailService.getEmailProvider(progress.recipient);
                    resultsDiv.innerHTML += `<div class="text-danger small">❌ فشل إرسال الرسالة إلى ${progress.recipient} (${provider}): ${progress.error}</div>`;
                }

                // Auto-scroll results
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }
        );

        // Show final results
        progressBar.style.width = '100%';
        statusDiv.innerHTML = `
            <div class="alert alert-info">
                <strong>انتهى الإرسال!</strong><br>
                📧 إجمالي الرسائل: ${results.total}<br>
                ✅ تم إرسال ${results.sent} رسالة بنجاح<br>
                ${results.failed > 0 ? `❌ فشل إرسال ${results.failed} رسالة` : ''}
                <br><br>
                <small>تم الإرسال إلى جميع مجالات البريد الإلكتروني: Gmail, Hotmail, Outlook, Yahoo وغيرها</small>
            </div>
        `;

        // Enable close button
        document.getElementById('emailProgressClose').disabled = false;

        // Store email log in Supabase
        await logEmailActivity(results.total, results.sent, results.failed, config.subject, results.errors);

    } catch (error) {
        console.error('Email batch sending failed:', error);

        statusDiv.innerHTML = `
            <div class="alert alert-danger">
                <strong>خطأ في إرسال الرسائل!</strong><br>
                ${error.message}
            </div>
        `;

        // Enable close button
        document.getElementById('emailProgressClose').disabled = false;
    }
}

/**
 * Get email configuration from form
 */
function getEmailConfig() {
    return {
        service: document.getElementById('emailService').value,
        senderEmail: document.getElementById('senderEmail').value,
        smtpHost: document.getElementById('smtpHost').value,
        smtpPort: parseInt(document.getElementById('smtpPort').value),
        smtpSecurity: document.getElementById('smtpSecurity').value,
        username: document.getElementById('emailUsername').value,
        password: document.getElementById('emailPassword').value,
        subject: document.getElementById('emailSubject').value,
        body: document.getElementById('emailBody').value,
        isHtml: document.getElementById('emailHtml').checked
    };
}

/**
 * Validate email configuration
 */
function validateEmailConfig(config) {
    if (!config.senderEmail) {
        window.showAlert('يرجى إدخال البريد الإلكتروني للمرسل', 'warning');
        return false;
    }

    if (!config.username || !config.password) {
        window.showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'warning');
        return false;
    }

    if (!config.subject || !config.body) {
        window.showAlert('يرجى إدخال موضوع الرسالة والمحتوى', 'warning');
        return false;
    }

    if (config.service === 'smtp' && (!config.smtpHost || !config.smtpPort)) {
        window.showAlert('يرجى إدخال إعدادات خادم SMTP', 'warning');
        return false;
    }

    return true;
}

/**
 * Parse email list from text
 */
function parseEmailList(text) {
    if (!text) return [];

    // Split by commas, semicolons, or newlines
    const emails = text.split(/[,;\n\r]+/)
        .map(email => email.trim())
        .filter(email => email && isValidEmail(email));

    return [...new Set(emails)]; // Remove duplicates
}

/**
 * Validate email address
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Save email configuration
 */
function saveEmailConfig(config) {
    try {
        const configToSave = {
            service: config.service,
            senderEmail: config.senderEmail,
            smtpHost: config.smtpHost,
            smtpPort: config.smtpPort,
            smtpSecurity: config.smtpSecurity,
            username: config.username
            // Note: We don't save the password for security reasons
        };

        localStorage.setItem('sallamchat_email_config', JSON.stringify(configToSave));
        console.log('Email configuration saved');
    } catch (error) {
        console.error('Error saving email configuration:', error);
    }
}

/**
 * Load email configuration
 */
function loadEmailConfig() {
    try {
        const saved = localStorage.getItem('sallamchat_email_config');
        if (saved) {
            const config = JSON.parse(saved);

            document.getElementById('emailService').value = config.service || 'smtp';
            document.getElementById('senderEmail').value = config.senderEmail || '';
            document.getElementById('smtpHost').value = config.smtpHost || '';
            document.getElementById('smtpPort').value = config.smtpPort || 587;
            document.getElementById('smtpSecurity').value = config.smtpSecurity || 'tls';
            document.getElementById('emailUsername').value = config.username || '';

            // Update UI based on service
            updateEmailConfig();
        }
    } catch (error) {
        console.error('Error loading email configuration:', error);
    }
}

/**
 * Close email progress modal
 */
function closeEmailProgress() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('emailProgressModal'));
    modal.hide();
}

/**
 * Log email activity to Supabase
 */
async function logEmailActivity(total, sent, failed, subject, errors = []) {
    try {
        if (window.useSupabase && window.supabaseClient) {
            const logData = {
                activity_type: 'email_sent',
                details: {
                    total_recipients: total,
                    successful_sends: sent,
                    failed_sends: failed,
                    subject: subject,
                    errors: errors.slice(0, 10), // Limit errors to first 10
                    timestamp: new Date().toISOString(),
                    success_rate: total > 0 ? ((sent / total) * 100).toFixed(2) + '%' : '0%'
                },
                created_at: new Date().toISOString()
            };

            // In a real implementation, you might want to create an admin_logs table
            console.log('Email activity logged:', logData);

            // You could also store this in a settings table for now
            try {
                await window.supabaseClient
                    .from('settings')
                    .insert({
                        key: `email_log_${Date.now()}`,
                        value: logData
                    });
                console.log('Email log stored in Supabase');
            } catch (dbError) {
                console.warn('Could not store email log in database:', dbError.message);
            }
        }
    } catch (error) {
        console.error('Error logging email activity:', error);
    }
}

// Make functions globally available
window.editUser = editUser;
window.deleteUser = deleteUser;
window.saveUserChanges = saveUserChanges;
window.showEmailModal = showEmailModal;
window.updateEmailConfig = updateEmailConfig;
window.updateRecipients = updateRecipients;
window.previewEmail = previewEmail;
window.testEmailConnection = testEmailConnection;
window.sendEmails = sendEmails;
window.closeEmailProgress = closeEmailProgress;

console.log('Admin panel with email functionality loaded successfully');
