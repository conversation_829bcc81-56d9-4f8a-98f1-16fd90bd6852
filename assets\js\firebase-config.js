/**
 * SallamChat - Firebase Configuration
 * This file contains Firebase initialization and configuration
 */

// Firebase configuration object
// Note: Replace these with your actual Firebase project credentials
const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "sallamchat-project.firebaseapp.com",
    projectId: "sallamchat-project",
    storageBucket: "sallamchat-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "1:123456789:web:abcdef123456789"
};

// Initialize Firebase
let app, auth, db, storage;

try {
    // Initialize Firebase App
    app = firebase.initializeApp(firebaseConfig);
    
    // Initialize Firebase services
    auth = firebase.auth();
    db = firebase.firestore();
    storage = firebase.storage();
    
    console.log('Firebase initialized successfully');
    
    // Enable offline persistence for Firestore
    db.enablePersistence()
        .then(() => {
            console.log('Firestore offline persistence enabled');
        })
        .catch((err) => {
            if (err.code === 'failed-precondition') {
                console.warn('Multiple tabs open, persistence can only be enabled in one tab at a time.');
            } else if (err.code === 'unimplemented') {
                console.warn('The current browser does not support all of the features required to enable persistence');
            }
        });
    
} catch (error) {
    console.error('Firebase initialization error:', error);
    
    // Fallback to localStorage if Firebase is not available
    console.warn('Falling back to localStorage for data persistence');
    
    // Mock Firebase services for development/offline use
    window.mockFirebase = true;
    
    // Create mock auth object
    auth = {
        currentUser: null,
        onAuthStateChanged: (callback) => {
            // Check localStorage for saved user
            const savedUser = localStorage.getItem('sallamchat_user');
            if (savedUser) {
                const user = JSON.parse(savedUser);
                auth.currentUser = user;
                callback(user);
            } else {
                callback(null);
            }
        },
        signInWithEmailAndPassword: (email, password) => {
            return new Promise((resolve, reject) => {
                // Mock authentication - check against localStorage users
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
                const user = users.find(u => u.email === email && u.password === password);
                
                if (user) {
                    const authUser = {
                        uid: user.id,
                        email: user.email,
                        displayName: user.fullName,
                        emailVerified: true
                    };
                    auth.currentUser = authUser;
                    localStorage.setItem('sallamchat_user', JSON.stringify(authUser));
                    resolve({ user: authUser });
                } else {
                    reject(new Error('Invalid email or password'));
                }
            });
        },
        createUserWithEmailAndPassword: (email, password) => {
            return new Promise((resolve, reject) => {
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
                
                // Check if user already exists
                if (users.find(u => u.email === email)) {
                    reject(new Error('User already exists'));
                    return;
                }
                
                const newUser = {
                    id: 'user_' + Date.now(),
                    email: email,
                    password: password,
                    fullName: '',
                    createdAt: new Date().toISOString(),
                    status: 'active'
                };
                
                users.push(newUser);
                localStorage.setItem('sallamchat_users', JSON.stringify(users));
                
                const authUser = {
                    uid: newUser.id,
                    email: newUser.email,
                    displayName: newUser.fullName,
                    emailVerified: true
                };
                
                auth.currentUser = authUser;
                localStorage.setItem('sallamchat_user', JSON.stringify(authUser));
                resolve({ user: authUser });
            });
        },
        signOut: () => {
            return new Promise((resolve) => {
                auth.currentUser = null;
                localStorage.removeItem('sallamchat_user');
                resolve();
            });
        },
        sendPasswordResetEmail: (email) => {
            return new Promise((resolve, reject) => {
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
                const user = users.find(u => u.email === email);
                
                if (user) {
                    // In a real app, this would send an email
                    console.log('Password reset email would be sent to:', email);
                    resolve();
                } else {
                    reject(new Error('User not found'));
                }
            });
        }
    };
    
    // Create mock Firestore object
    db = {
        collection: (collectionName) => ({
            doc: (docId) => ({
                set: (data) => {
                    return new Promise((resolve) => {
                        const key = `sallamchat_${collectionName}_${docId}`;
                        localStorage.setItem(key, JSON.stringify(data));
                        resolve();
                    });
                },
                get: () => {
                    return new Promise((resolve) => {
                        const key = `sallamchat_${collectionName}_${docId}`;
                        const data = localStorage.getItem(key);
                        resolve({
                            exists: !!data,
                            data: () => data ? JSON.parse(data) : null
                        });
                    });
                },
                update: (data) => {
                    return new Promise((resolve) => {
                        const key = `sallamchat_${collectionName}_${docId}`;
                        const existing = localStorage.getItem(key);
                        if (existing) {
                            const updated = { ...JSON.parse(existing), ...data };
                            localStorage.setItem(key, JSON.stringify(updated));
                        }
                        resolve();
                    });
                },
                delete: () => {
                    return new Promise((resolve) => {
                        const key = `sallamchat_${collectionName}_${docId}`;
                        localStorage.removeItem(key);
                        resolve();
                    });
                }
            }),
            add: (data) => {
                return new Promise((resolve) => {
                    const id = Date.now().toString();
                    const key = `sallamchat_${collectionName}_${id}`;
                    const docData = { ...data, id, createdAt: new Date().toISOString() };
                    localStorage.setItem(key, JSON.stringify(docData));
                    resolve({ id });
                });
            },
            where: (field, operator, value) => ({
                get: () => {
                    return new Promise((resolve) => {
                        const docs = [];
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            if (key && key.startsWith(`sallamchat_${collectionName}_`)) {
                                const data = JSON.parse(localStorage.getItem(key));
                                if (data[field] === value) {
                                    docs.push({
                                        id: data.id,
                                        data: () => data
                                    });
                                }
                            }
                        }
                        resolve({ docs });
                    });
                }
            }),
            orderBy: (field, direction = 'asc') => ({
                get: () => {
                    return new Promise((resolve) => {
                        const docs = [];
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            if (key && key.startsWith(`sallamchat_${collectionName}_`)) {
                                const data = JSON.parse(localStorage.getItem(key));
                                docs.push({
                                    id: data.id,
                                    data: () => data
                                });
                            }
                        }
                        
                        // Sort documents
                        docs.sort((a, b) => {
                            const aVal = a.data()[field];
                            const bVal = b.data()[field];
                            if (direction === 'desc') {
                                return bVal > aVal ? 1 : -1;
                            }
                            return aVal > bVal ? 1 : -1;
                        });
                        
                        resolve({ docs });
                    });
                }
            }),
            onSnapshot: (callback) => {
                // Mock real-time updates
                const interval = setInterval(() => {
                    const docs = [];
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && key.startsWith(`sallamchat_${collectionName}_`)) {
                            const data = JSON.parse(localStorage.getItem(key));
                            docs.push({
                                id: data.id,
                                data: () => data
                            });
                        }
                    }
                    callback({ docs });
                }, 1000);
                
                return () => clearInterval(interval);
            }
        })
    };
    
    // Create mock storage object
    storage = {
        ref: (path) => ({
            put: (file) => {
                return new Promise((resolve) => {
                    // Mock file upload
                    const reader = new FileReader();
                    reader.onload = () => {
                        const key = `sallamchat_storage_${path}`;
                        localStorage.setItem(key, reader.result);
                        resolve({
                            ref: {
                                getDownloadURL: () => Promise.resolve(reader.result)
                            }
                        });
                    };
                    reader.readAsDataURL(file);
                });
            }
        })
    };
}

// Export Firebase services for use in other files
window.firebaseAuth = auth;
window.firebaseDB = db;
window.firebaseStorage = storage;

// Initialize admin user if not exists (for mock mode)
if (window.mockFirebase) {
    const adminUsers = JSON.parse(localStorage.getItem('sallamchat_admins') || '[]');
    if (adminUsers.length === 0) {
        const defaultAdmin = {
            username: 'ADMIN',
            password: '*#admin1981#*',
            createdAt: new Date().toISOString()
        };
        localStorage.setItem('sallamchat_admins', JSON.stringify([defaultAdmin]));
    }
}

console.log('Firebase configuration loaded successfully');
