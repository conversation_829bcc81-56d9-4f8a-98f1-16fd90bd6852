/**
 * SallamChat - Firebase Configuration
 * This file contains Firebase initialization and configuration
 */

// Firebase configuration object
// Note: Replace these with your actual Firebase project credentials
const firebaseConfig = {
  apiKey: "AIzaSyBhYX4m7eNLARHavKcNcvxz4ci6JrxMB5U",
  authDomain: "sallam-chat.firebaseapp.com",
  databaseURL: "https://sallam-chat-default-rtdb.firebaseio.com",
  projectId: "sallam-chat",
  storageBucket: "sallam-chat.firebasestorage.app",
  messagingSenderId: "1086057558353",
  appId: "1:1086057558353:web:079e5eb2462b3f2cf16370",
  measurementId: "G-4PT5B0KQ9Q"
};

// Initialize Firebase App, Auth, Firestore, and Storage
let app, auth, db, storage;

// Initialize auth, db, and storage as null first
auth = null;
db = null;
storage = null;

try {
    // Try Supabase first if available
    if (typeof supabase !== 'undefined' && window.initializeSupabase) {
        console.log('Supabase available, initializing...');
        window.initializeSupabase().then(() => {
            window.useSupabase = true;
            console.log('Supabase initialized successfully');
        }).catch(error => {
            console.error('Supabase initialization failed:', error);
            // Continue with Firebase or fallback
            initializeFirebaseOrFallback();
        });
        return;
    }

    initializeFirebaseOrFallback();

} catch (error) {
    console.error('Database initialization error:', error);
    initializeFallbackMode();
}

function initializeFirebaseOrFallback() {
    try {
        // Check if Firebase is available
        if (typeof firebase === 'undefined') {
            throw new Error('Firebase SDK not loaded');
        }

        // Initialize Firebase App
        app = firebase.initializeApp(firebaseConfig);

        // Initialize Firebase services
        auth = firebase.auth();
        db = firebase.firestore();
        storage = firebase.storage();

        console.log('Firebase initialized successfully');

        // Enable offline persistence for Firestore
        db.enablePersistence()
            .then(() => {
                console.log('Firestore offline persistence enabled');
            })
            .catch((err) => {
                if (err.code === 'failed-precondition') {
                    console.warn('Multiple tabs open, persistence can only be enabled in one tab at a time.');
                } else if (err.code === 'unimplemented') {
                    console.warn('The current browser does not support all of the features required to enable persistence');
                }
            });

        // Export Firebase services
        window.firebaseAuth = auth;
        window.firebaseDB = db;
        window.firebaseStorage = storage;

    } catch (error) {
        console.error('Firebase initialization error:', error);
        initializeFallbackMode();
    }
}

function initializeFallbackMode() {
    // Try IndexedDB first, then fallback to localStorage
    if (window.indexedDB) {
        console.warn('Falling back to IndexedDB for data persistence');
        initializeIndexedDBMode().catch(err => {
            console.error('IndexedDB initialization failed:', err);
            console.warn('Falling back to localStorage for data persistence');
            window.mockFirebase = true;
            initializeLocalStorageMode();
        });
    } else {
        console.warn('Falling back to localStorage for data persistence');
        window.mockFirebase = true;
        initializeLocalStorageMode();
    }
}

/**
 * Initialize IndexedDB mode
 */
async function initializeIndexedDBMode() {
    try {
        await window.sallamChatDB.init();

        // Create mock auth object for IndexedDB
        auth = {
            currentUser: null,
            onAuthStateChanged: (callback) => {
                // Check for saved user session
                window.sallamChatDB.getSetting('currentUser').then(savedUser => {
                    if (savedUser) {
                        auth.currentUser = savedUser;
                        callback(savedUser);
                    } else {
                        callback(null);
                    }
                }).catch(() => callback(null));
            },
            signInWithEmailAndPassword: async (email, password) => {
                const user = await window.sallamChatDB.getUserByEmail(email);
                if (user && user.password === password) {
                    const authUser = {
                        uid: user.id,
                        email: user.email,
                        displayName: user.fullName,
                        emailVerified: true
                    };
                    auth.currentUser = authUser;
                    await window.sallamChatDB.setSetting('currentUser', authUser);
                    return { user: authUser };
                } else {
                    throw new Error('Invalid email or password');
                }
            },
            createUserWithEmailAndPassword: async (email, password) => {
                const existingUser = await window.sallamChatDB.getUserByEmail(email);
                if (existingUser) {
                    throw new Error('User already exists');
                }

                const newUser = {
                    id: window.sallamChatDB.generateId(),
                    email: email,
                    password: password,
                    fullName: '',
                    status: 'active'
                };

                await window.sallamChatDB.addUser(newUser);

                const authUser = {
                    uid: newUser.id,
                    email: newUser.email,
                    displayName: newUser.fullName,
                    emailVerified: true
                };

                auth.currentUser = authUser;
                await window.sallamChatDB.setSetting('currentUser', authUser);
                return { user: authUser };
            },
            signOut: async () => {
                auth.currentUser = null;
                await window.sallamChatDB.setSetting('currentUser', null);
            },
            sendPasswordResetEmail: async (email) => {
                const user = await window.sallamChatDB.getUserByEmail(email);
                if (user) {
                    console.log('Password reset email would be sent to:', email);
                } else {
                    throw new Error('User not found');
                }
            }
        };

        // Create mock Firestore object for IndexedDB
        db = {
            collection: (collectionName) => ({
                doc: (docId) => ({
                    set: async (data) => {
                        data.id = docId;
                        await window.sallamChatDB.update(collectionName, data);
                    },
                    get: async () => {
                        const data = await window.sallamChatDB.get(collectionName, docId);
                        return {
                            exists: !!data,
                            data: () => data
                        };
                    },
                    update: async (data) => {
                        const existing = await window.sallamChatDB.get(collectionName, docId);
                        if (existing) {
                            const updated = { ...existing, ...data };
                            await window.sallamChatDB.update(collectionName, updated);
                        }
                    },
                    delete: async () => {
                        await window.sallamChatDB.delete(collectionName, docId);
                    },
                    collection: (subCollection) => ({
                        add: async (data) => {
                            data.chatId = docId;
                            const result = await window.sallamChatDB.add(subCollection, data);
                            return { id: result.id };
                        },
                        orderBy: (field, direction = 'asc') => ({
                            onSnapshot: (callback) => {
                                // Mock real-time updates for messages
                                const interval = setInterval(async () => {
                                    try {
                                        let docs;
                                        if (subCollection === 'messages') {
                                            docs = await window.sallamChatDB.getMessagesByChat(docId);
                                        } else {
                                            docs = await window.sallamChatDB.queryByIndex(subCollection, 'chatId', docId);
                                        }

                                        if (direction === 'desc') {
                                            docs.reverse();
                                        }

                                        const snapshot = {
                                            docs: docs.map(doc => ({
                                                id: doc.id,
                                                data: () => ({
                                                    ...doc,
                                                    timestamp: doc.timestamp ? { toDate: () => new Date(doc.timestamp) } : null
                                                })
                                            }))
                                        };
                                        callback(snapshot);
                                    } catch (error) {
                                        console.error('Error in onSnapshot:', error);
                                    }
                                }, 1000);

                                return () => clearInterval(interval);
                            }
                        })
                    })
                }),
                add: async (data) => {
                    const result = await window.sallamChatDB.add(collectionName, data);
                    return { id: result.id };
                },
                where: (field, operator, value) => ({
                    get: async () => {
                        let docs;
                        if (field === 'participants' && operator === 'array-contains') {
                            docs = await window.sallamChatDB.getChatsByUser(value);
                        } else {
                            docs = await window.sallamChatDB.queryByIndex(collectionName, field, value);
                        }
                        return {
                            docs: docs.map(doc => ({
                                id: doc.id,
                                data: () => ({
                                    ...doc,
                                    lastMessageTime: doc.lastMessageTime ? { toDate: () => new Date(doc.lastMessageTime) } : null
                                })
                            }))
                        };
                    },
                    orderBy: (field, direction = 'asc') => ({
                        get: async () => {
                            let docs;
                            if (field === 'participants' && operator === 'array-contains') {
                                docs = await window.sallamChatDB.getChatsByUser(value);
                            } else {
                                docs = await window.sallamChatDB.queryByIndex(collectionName, field, value);
                            }

                            // Sort by the specified field
                            docs.sort((a, b) => {
                                const aVal = a[field];
                                const bVal = b[field];
                                if (direction === 'desc') {
                                    return new Date(bVal) - new Date(aVal);
                                }
                                return new Date(aVal) - new Date(bVal);
                            });

                            return {
                                docs: docs.map(doc => ({
                                    id: doc.id,
                                    data: () => ({
                                        ...doc,
                                        lastMessageTime: doc.lastMessageTime ? { toDate: () => new Date(doc.lastMessageTime) } : null
                                    })
                                }))
                            };
                        }
                    })
                }),
                orderBy: (field, direction = 'asc') => ({
                    get: async () => {
                        const docs = await window.sallamChatDB.getAll(collectionName);

                        // Sort documents
                        docs.sort((a, b) => {
                            const aVal = a[field];
                            const bVal = b[field];
                            if (direction === 'desc') {
                                return new Date(bVal) - new Date(aVal);
                            }
                            return new Date(aVal) - new Date(bVal);
                        });

                        return {
                            docs: docs.map(doc => ({
                                id: doc.id,
                                data: () => doc
                            }))
                        };
                    }
                }),
                get: async () => {
                    const docs = await window.sallamChatDB.getAll(collectionName);
                    return {
                        docs: docs.map(doc => ({
                            id: doc.id,
                            data: () => doc
                        })),
                        size: docs.length
                    };
                },
                onSnapshot: (callback) => {
                    // Mock real-time updates
                    const interval = setInterval(async () => {
                        try {
                            const docs = await window.sallamChatDB.getAll(collectionName);
                            const snapshot = {
                                docs: docs.map(doc => ({
                                    id: doc.id,
                                    data: () => doc
                                })),
                                docChanges: () => [] // Simplified for now
                            };
                            callback(snapshot);
                        } catch (error) {
                            console.error('Error in collection onSnapshot:', error);
                        }
                    }, 2000);

                    return () => clearInterval(interval);
                }
            })
        };

        // Create mock storage object for IndexedDB
        storage = {
            ref: (path) => ({
                put: async (file) => {
                    // Store file in IndexedDB
                    const fileData = {
                        id: window.sallamChatDB.generateId(),
                        path: path,
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        data: await fileToBase64(file)
                    };

                    await window.sallamChatDB.addFile(fileData);

                    return {
                        ref: {
                            getDownloadURL: () => Promise.resolve(`data:${file.type};base64,${fileData.data}`)
                        }
                    };
                }
            })
        };

        console.log('IndexedDB mode initialized successfully');

        // Export services
        window.firebaseAuth = auth;
        window.firebaseDB = db;
        window.firebaseStorage = storage;

    } catch (error) {
        console.error('Failed to initialize IndexedDB, falling back to localStorage:', error);
        window.mockFirebase = true;
        initializeLocalStorageMode();
    }
}

/**
 * Helper function to convert file to base64
 */
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = error => reject(error);
    });
}

/**
 * Initialize localStorage mode (existing implementation)
 */
function initializeLocalStorageMode() {
    console.log('Initializing localStorage mode...');

    // Create mock auth object
    auth = {
        currentUser: null,
        onAuthStateChanged: (callback) => {
            // Check localStorage for saved user
            const savedUser = localStorage.getItem('sallamchat_user');
            if (savedUser) {
                const user = JSON.parse(savedUser);
                auth.currentUser = user;
                callback(user);
            } else {
                callback(null);
            }
        },
        signInWithEmailAndPassword: (email, password) => {
            return new Promise((resolve, reject) => {
                // Mock authentication - check against localStorage users
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
                const user = users.find(u => u.email === email && u.password === password);

                if (user) {
                    const authUser = {
                        uid: user.id,
                        email: user.email,
                        displayName: user.fullName,
                        emailVerified: true
                    };
                    auth.currentUser = authUser;
                    localStorage.setItem('sallamchat_user', JSON.stringify(authUser));
                    resolve({ user: authUser });
                } else {
                    reject(new Error('Invalid email or password'));
                }
            });
        },
        createUserWithEmailAndPassword: (email, password) => {
            return new Promise((resolve, reject) => {
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');

                // Check if user already exists
                if (users.find(u => u.email === email)) {
                    reject(new Error('User already exists'));
                    return;
                }

                const newUser = {
                    id: 'user_' + Date.now(),
                    email: email,
                    password: password,
                    fullName: '',
                    createdAt: new Date().toISOString(),
                    status: 'active'
                };

                users.push(newUser);
                localStorage.setItem('sallamchat_users', JSON.stringify(users));

                const authUser = {
                    uid: newUser.id,
                    email: newUser.email,
                    displayName: newUser.fullName,
                    emailVerified: true
                };

                auth.currentUser = authUser;
                localStorage.setItem('sallamchat_user', JSON.stringify(authUser));
                resolve({ user: authUser });
            });
        },
        signOut: () => {
            return new Promise((resolve) => {
                auth.currentUser = null;
                localStorage.removeItem('sallamchat_user');
                resolve();
            });
        },
        sendPasswordResetEmail: (email) => {
            return new Promise((resolve, reject) => {
                const users = JSON.parse(localStorage.getItem('sallamchat_users') || '[]');
                const user = users.find(u => u.email === email);

                if (user) {
                    // In a real app, this would send an email
                    console.log('Password reset email would be sent to:', email);
                    resolve();
                } else {
                    reject(new Error('User not found'));
                }
            });
        }
    };

    // Create mock Firestore object
    db = {
        collection: (collectionName) => ({
            doc: (docId) => ({
                set: (data) => {
                    return new Promise((resolve) => {
                        const key = `sallamchat_${collectionName}_${docId}`;
                        localStorage.setItem(key, JSON.stringify(data));
                        resolve();
                    });
                },
                get: () => {
                    return new Promise((resolve) => {
                        const key = `sallamchat_${collectionName}_${docId}`;
                        const data = localStorage.getItem(key);
                        resolve({
                            exists: !!data,
                            data: () => data ? JSON.parse(data) : null
                        });
                    });
                },
                update: (data) => {
                    return new Promise((resolve) => {
                        const key = `sallamchat_${collectionName}_${docId}`;
                        const existing = localStorage.getItem(key);
                        if (existing) {
                            const updated = { ...JSON.parse(existing), ...data };
                            localStorage.setItem(key, JSON.stringify(updated));
                        }
                        resolve();
                    });
                },
                delete: () => {
                    return new Promise((resolve) => {
                        const key = `sallamchat_${collectionName}_${docId}`;
                        localStorage.removeItem(key);
                        resolve();
                    });
                }
            }),
            add: (data) => {
                return new Promise((resolve) => {
                    const id = Date.now().toString();
                    const key = `sallamchat_${collectionName}_${id}`;
                    const docData = { ...data, id, createdAt: new Date().toISOString() };
                    localStorage.setItem(key, JSON.stringify(docData));
                    resolve({ id });
                });
            },
            where: (field, operator, value) => ({
                get: () => {
                    return new Promise((resolve) => {
                        const docs = [];
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            if (key && key.startsWith(`sallamchat_${collectionName}_`)) {
                                const data = JSON.parse(localStorage.getItem(key));
                                if (data[field] === value) {
                                    docs.push({
                                        id: data.id,
                                        data: () => data
                                    });
                                }
                            }
                        }
                        resolve({ docs });
                    });
                }
            }),
            orderBy: (field, direction = 'asc') => ({
                get: () => {
                    return new Promise((resolve) => {
                        const docs = [];
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            if (key && key.startsWith(`sallamchat_${collectionName}_`)) {
                                const data = JSON.parse(localStorage.getItem(key));
                                docs.push({
                                    id: data.id,
                                    data: () => data
                                });
                            }
                        }

                        // Sort documents
                        docs.sort((a, b) => {
                            const aVal = a.data()[field];
                            const bVal = b.data()[field];
                            if (direction === 'desc') {
                                return bVal > aVal ? 1 : -1;
                            }
                            return aVal > bVal ? 1 : -1;
                        });

                        resolve({ docs });
                    });
                }
            }),
            onSnapshot: (callback) => {
                // Mock real-time updates
                const interval = setInterval(() => {
                    const docs = [];
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && key.startsWith(`sallamchat_${collectionName}_`)) {
                            const data = JSON.parse(localStorage.getItem(key));
                            docs.push({
                                id: data.id,
                                data: () => data
                            });
                        }
                    }
                    callback({ docs });
                }, 1000);

                return () => clearInterval(interval);
            }
        })
    };

    // Create mock storage object
    storage = {
        ref: (path) => ({
            put: (file) => {
                return new Promise((resolve) => {
                    // Mock file upload
                    const reader = new FileReader();
                    reader.onload = () => {
                        const key = `sallamchat_storage_${path}`;
                        localStorage.setItem(key, reader.result);
                        resolve({
                            ref: {
                                getDownloadURL: () => Promise.resolve(reader.result)
                            }
                        });
                    };
                    reader.readAsDataURL(file);
                });
            }
        })
    };

    console.log('LocalStorage mode initialized successfully');

    // Export services
    window.firebaseAuth = auth;
    window.firebaseDB = db;
    window.firebaseStorage = storage;
}

// Initialize fallback mode immediately if Firebase is not available
if (typeof firebase === 'undefined') {
    console.warn('Firebase SDK not loaded, initializing fallback mode');
    if (window.indexedDB) {
        initializeIndexedDBMode().catch(err => {
            console.error('IndexedDB initialization failed:', err);
            window.mockFirebase = true;
            initializeLocalStorageMode();
        });
    } else {
        window.mockFirebase = true;
        initializeLocalStorageMode();
    }
}

// Export Firebase services for use in other files (will be set by initialization functions)
window.firebaseAuth = auth;
window.firebaseDB = db;
window.firebaseStorage = storage;

// Initialize admin user if not exists (for mock mode)
function initializeDefaultAdmin() {
    if (window.mockFirebase) {
        const adminUsers = JSON.parse(localStorage.getItem('sallamchat_admins') || '[]');
        if (adminUsers.length === 0) {
            const defaultAdmin = {
                username: 'ADMIN',
                password: '*#admin1981#*',
                createdAt: new Date().toISOString()
            };
            localStorage.setItem('sallamchat_admins', JSON.stringify([defaultAdmin]));
        }
    }
}

// Call admin initialization after a short delay to ensure other systems are ready
setTimeout(initializeDefaultAdmin, 100);

console.log('Firebase configuration loaded successfully');
