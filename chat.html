<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المحادثات - SallamChat</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/themes.css" rel="stylesheet">
</head>
<body class="chat-page">
    <!-- Navigation Header -->
    <nav class="navbar navbar-expand-lg chat-navbar">
        <div class="container-fluid">
            <!-- Logo -->
            <div class="navbar-brand d-flex align-items-center">
                <img src="assets/images/logo.png" alt="SallamChat Logo" class="navbar-logo me-2"
                     onerror="this.src='assets/images/logo-fallback.svg'">
                <span class="fw-bold">SallamChat</span>
            </div>

            <!-- User Info & Controls -->
            <div class="navbar-nav ms-auto d-flex flex-row align-items-center">
                <!-- Theme Toggle -->
                <button class="btn btn-outline-secondary me-2" id="themeToggle">
                    <i class="bi bi-moon-fill" id="themeIcon"></i>
                </button>

                <!-- User Menu -->
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i>
                        <span id="userDisplayName">المستخدم</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#" id="profileSettings">
                            <i class="bi bi-gear me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" id="logoutBtn">
                            <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Chat Container -->
    <div class="container-fluid chat-container">
        <div class="row h-100">
            <!-- Sidebar - Chat List -->
            <div class="col-md-4 col-lg-3 chat-sidebar">
                <div class="sidebar-header">
                    <h5 class="mb-3">المحادثات</h5>
                    <button class="btn btn-primary btn-sm w-100 mb-3" id="newChatBtn">
                        <i class="bi bi-plus-circle me-2"></i>
                        محادثة جديدة
                    </button>
                </div>

                <!-- Search -->
                <div class="search-container mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" class="form-control" placeholder="البحث في المحادثات..." id="searchChats">
                    </div>
                </div>

                <!-- Chat List -->
                <div class="chat-list" id="chatList">
                    <!-- Chat items will be dynamically loaded here -->
                </div>
            </div>

            <!-- Main Chat Area -->
            <div class="col-md-8 col-lg-9 chat-main">
                <!-- Welcome Screen -->
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="text-center">
                        <img src="assets/images/logo.png" alt="SallamChat Logo" class="welcome-logo mb-4"
                             onerror="this.src='assets/images/logo-fallback.svg'">
                        <h3>مرحباً بك في SallamChat</h3>
                        <p class="text-muted">اختر محادثة من القائمة أو ابدأ محادثة جديدة</p>
                    </div>
                </div>

                <!-- Active Chat -->
                <div class="active-chat d-none" id="activeChat">
                    <!-- Chat Header -->
                    <div class="chat-header">
                        <div class="d-flex align-items-center">
                            <div class="chat-avatar me-3">
                                <i class="bi bi-person-circle"></i>
                            </div>
                            <div class="chat-info">
                                <h6 class="mb-0" id="activeChatName">اسم المستخدم</h6>
                                <small class="text-muted" id="activeChatStatus">متصل</small>
                            </div>
                        </div>
                        <div class="chat-actions">
                            <button class="btn btn-outline-success btn-sm me-2" id="voiceCallBtn">
                                <i class="bi bi-telephone"></i>
                            </button>
                            <button class="btn btn-outline-primary btn-sm" id="videoCallBtn">
                                <i class="bi bi-camera-video"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Messages Container -->
                    <div class="messages-container" id="messagesContainer">
                        <!-- Messages will be dynamically loaded here -->
                    </div>

                    <!-- Message Input -->
                    <div class="message-input-container">
                        <div class="input-group">
                            <button class="btn btn-outline-secondary" type="button" id="attachFileBtn">
                                <i class="bi bi-paperclip"></i>
                            </button>
                            <input type="text" class="form-control" placeholder="اكتب رسالتك هنا..." id="messageInput">
                            <button class="btn btn-outline-primary" type="button" id="voiceRecordBtn">
                                <i class="bi bi-mic"></i>
                            </button>
                            <button class="btn btn-primary" type="button" id="sendMessageBtn">
                                <i class="bi bi-send"></i>
                            </button>
                        </div>

                        <!-- Voice Recording Indicator -->
                        <div class="voice-recording d-none" id="voiceRecording">
                            <div class="d-flex align-items-center justify-content-center py-2">
                                <div class="recording-animation me-2"></div>
                                <span>جاري التسجيل... <span id="recordingTime">00:00</span></span>
                                <button class="btn btn-danger btn-sm ms-3" id="stopRecordingBtn">
                                    <i class="bi bi-stop-fill"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Chat Modal -->
    <div class="modal fade" id="newChatModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">محادثة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="searchUsers" class="form-label">البحث عن مستخدم</label>
                        <input type="text" class="form-control" id="searchUsers" placeholder="ادخل البريد الإلكتروني أو الاسم">
                    </div>
                    <div id="userSearchResults">
                        <!-- Search results will appear here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Settings Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الإعدادات الشخصية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="profileForm">
                        <div class="mb-3">
                            <label for="profileName" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="profileName">
                        </div>
                        <div class="mb-3">
                            <label for="profileEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="profileEmail" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="profileStatus" class="form-label">الحالة</label>
                            <select class="form-select" id="profileStatus">
                                <option value="online">متصل</option>
                                <option value="away">غائب</option>
                                <option value="busy">مشغول</option>
                                <option value="offline">غير متصل</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveProfileBtn">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden file input for attachments -->
    <input type="file" id="fileInput" style="display: none;" accept="image/*,audio/*,video/*,.pdf,.doc,.docx">

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Supabase SDK (Primary Database) -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Custom JS -->
    <script src="assets/js/indexeddb-wrapper.js"></script>
    <script src="assets/js/supabase-config.js"></script>
    <script src="assets/js/database-config.js"></script>
    <script src="assets/js/chat.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>
