<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SallamChat - Test Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/themes.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="text-center mb-5">
                    <div class="logo-container">
                        <div class="logo-circle">
                            <i class="bi bi-chat-dots-fill"></i>
                        </div>
                    </div>
                    <h1 class="app-title mt-3">SallamChat</h1>
                    <p class="app-tagline">للتواصل الحر والسلمي</p>
                </div>

                <div class="auth-card">
                    <div class="card-header text-center">
                        <h3>🧪 اختبار SallamChat</h3>
                        <p class="text-muted">تحقق من جميع مكونات التطبيق</p>
                    </div>
                    <div class="card-body">
                        <div class="test-section mb-4">
                            <h5><i class="bi bi-check-circle text-success me-2"></i>الصفحات المتاحة</h5>
                            <div class="d-grid gap-2">
                                <a href="index.html" class="btn btn-outline-primary">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>صفحة تسجيل الدخول
                                </a>
                                <a href="register.html" class="btn btn-outline-primary">
                                    <i class="bi bi-person-plus me-2"></i>صفحة التسجيل
                                </a>
                                <a href="chat.html" class="btn btn-outline-primary">
                                    <i class="bi bi-chat-dots me-2"></i>واجهة المحادثة
                                </a>
                                <a href="admin.html" class="btn btn-outline-danger">
                                    <i class="bi bi-shield-lock me-2"></i>لوحة الإدارة
                                </a>
                            </div>
                        </div>

                        <div class="test-section mb-4">
                            <h5><i class="bi bi-palette text-primary me-2"></i>اختبار الثيمات</h5>
                            <button class="btn btn-outline-secondary" id="testTheme">
                                <i class="bi bi-moon-fill me-2"></i>تبديل الوضع الليلي/النهاري
                            </button>
                        </div>

                        <div class="test-section mb-4">
                            <h5><i class="bi bi-database text-info me-2"></i>اختبار قاعدة البيانات</h5>
                            <button class="btn btn-outline-info" id="testDB">
                                <i class="bi bi-database me-2"></i>اختبار LocalStorage
                            </button>
                            <div id="dbResult" class="mt-2"></div>
                        </div>

                        <div class="test-section mb-4">
                            <h5><i class="bi bi-volume-up text-warning me-2"></i>اختبار الأصوات</h5>
                            <div class="d-grid gap-2">
                                <a href="generate-sounds.html" class="btn btn-outline-warning">
                                    <i class="bi bi-music-note me-2"></i>إنشاء ملفات الصوت
                                </a>
                            </div>
                        </div>

                        <div class="test-section">
                            <h5><i class="bi bi-info-circle text-secondary me-2"></i>معلومات النظام</h5>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-1"><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
                                <p class="mb-1"><strong>حجم الشاشة:</strong> <span id="screenInfo"></span></p>
                                <p class="mb-1"><strong>الوقت:</strong> <span id="timeInfo"></span></p>
                                <p class="mb-0"><strong>الحالة:</strong> <span class="text-success">جاهز للاستخدام</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <p class="text-muted">
                        <i class="bi bi-heart-fill text-danger me-1"></i>
                        تم تطوير SallamChat بواسطة م. هاني سلام
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div class="alert-container" id="alertContainer"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/firebase-config.js"></script>
    <script src="assets/js/app.js"></script>
    
    <script>
        // Test functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Update system info
            updateSystemInfo();
            
            // Theme toggle test
            document.getElementById('testTheme').addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                document.documentElement.setAttribute('data-theme', newTheme);
                
                const icon = this.querySelector('i');
                if (newTheme === 'dark') {
                    icon.className = 'bi bi-sun-fill me-2';
                    this.innerHTML = '<i class="bi bi-sun-fill me-2"></i>تبديل للوضع النهاري';
                } else {
                    icon.className = 'bi bi-moon-fill me-2';
                    this.innerHTML = '<i class="bi bi-moon-fill me-2"></i>تبديل للوضع الليلي';
                }
                
                window.showAlert(`تم التبديل إلى الوضع ${newTheme === 'dark' ? 'الليلي' : 'النهاري'}`, 'success', 2000);
            });
            
            // Database test
            document.getElementById('testDB').addEventListener('click', function() {
                try {
                    // Test localStorage
                    const testData = {
                        timestamp: new Date().toISOString(),
                        test: 'SallamChat Database Test'
                    };
                    
                    localStorage.setItem('sallamchat_test', JSON.stringify(testData));
                    const retrieved = JSON.parse(localStorage.getItem('sallamchat_test'));
                    
                    if (retrieved && retrieved.test === testData.test) {
                        document.getElementById('dbResult').innerHTML = 
                            '<div class="alert alert-success mt-2">✅ قاعدة البيانات تعمل بشكل صحيح</div>';
                        window.showAlert('اختبار قاعدة البيانات نجح!', 'success');
                    } else {
                        throw new Error('Data mismatch');
                    }
                    
                    // Clean up
                    localStorage.removeItem('sallamchat_test');
                    
                } catch (error) {
                    document.getElementById('dbResult').innerHTML = 
                        '<div class="alert alert-danger mt-2">❌ خطأ في قاعدة البيانات: ' + error.message + '</div>';
                    window.showAlert('فشل اختبار قاعدة البيانات', 'danger');
                }
            });
        });
        
        function updateSystemInfo() {
            // Browser info
            const browserInfo = navigator.userAgent.split(' ').pop();
            document.getElementById('browserInfo').textContent = browserInfo;
            
            // Screen info
            const screenInfo = `${window.screen.width}x${window.screen.height}`;
            document.getElementById('screenInfo').textContent = screenInfo;
            
            // Time info
            const timeInfo = new Date().toLocaleString('ar-SA');
            document.getElementById('timeInfo').textContent = timeInfo;
            
            // Update time every second
            setInterval(() => {
                document.getElementById('timeInfo').textContent = new Date().toLocaleString('ar-SA');
            }, 1000);
        }
    </script>
</body>
</html>
