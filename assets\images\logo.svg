<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" filter="url(#shadow)"/>
  
  <!-- Chat Bubble 1 -->
  <path d="M25 35 Q20 30 25 25 L40 25 Q45 25 45 30 L45 40 Q45 45 40 45 L30 45 L25 50 Z" 
        fill="white" opacity="0.9"/>
  
  <!-- Chat Bubble 2 -->
  <path d="M75 45 Q80 40 75 35 L60 35 Q55 35 55 40 L55 50 Q55 55 60 55 L70 55 L75 60 Z" 
        fill="white" opacity="0.8"/>
  
  <!-- Chat Dots in Bubble 1 -->
  <circle cx="30" cy="35" r="2" fill="url(#logoGradient)"/>
  <circle cx="35" cy="35" r="2" fill="url(#logoGradient)"/>
  <circle cx="40" cy="35" r="2" fill="url(#logoGradient)"/>
  
  <!-- Chat Dots in Bubble 2 -->
  <circle cx="60" cy="45" r="2" fill="url(#logoGradient)"/>
  <circle cx="65" cy="45" r="2" fill="url(#logoGradient)"/>
  <circle cx="70" cy="45" r="2" fill="url(#logoGradient)"/>
  
  <!-- Peace Symbol -->
  <circle cx="50" cy="70" r="8" fill="none" stroke="white" stroke-width="2" opacity="0.9"/>
  <line x1="50" y1="62" x2="50" y2="78" stroke="white" stroke-width="2" opacity="0.9"/>
  <line x1="44" y1="68" x2="50" y2="74" stroke="white" stroke-width="2" opacity="0.9"/>
  <line x1="56" y1="68" x2="50" y2="74" stroke="white" stroke-width="2" opacity="0.9"/>
</svg>
