<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - SallamChat</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/themes.css" rel="stylesheet">
</head>
<body class="register-page">
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" id="themeToggle">
        <i class="bi bi-moon-fill" id="themeIcon"></i>
    </button>

    <div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center py-4">
        <div class="row w-100 justify-content-center">
            <div class="col-md-6 col-lg-5">
                <!-- Logo Section -->
                <div class="text-center mb-4">
                    <div class="logo-container">
                        <img src="assets/images/logo.png" alt="SallamChat Logo" class="app-logo"
                             onerror="this.src='assets/images/logo-fallback.svg'">
                    </div>
                    <h1 class="app-title mt-3">SallamChat</h1>
                    <p class="app-tagline">للتواصل الحر والسلمي</p>
                </div>

                <!-- Registration Form -->
                <div class="auth-card">
                    <div class="card-header text-center">
                        <h3>إنشاء حساب جديد</h3>
                        <p class="text-muted mb-0">انضم إلى مجتمع SallamChat</p>
                    </div>
                    <div class="card-body">
                        <form id="registerForm">
                            <div class="mb-3">
                                <label for="fullName" class="form-label">الاسم الكامل</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-person"></i>
                                    </span>
                                    <input type="text" class="form-control" id="fullName" required minlength="3">
                                </div>
                                <div class="form-text">يجب أن يكون الاسم 3 أحرف على الأقل</div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" required minlength="6">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                            </div>

                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-lock-fill"></i>
                                    </span>
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Password Strength Indicator -->
                            <div class="password-strength mb-3" id="passwordStrength">
                                <div class="strength-bar">
                                    <div class="strength-fill" id="strengthFill"></div>
                                </div>
                                <small class="strength-text" id="strengthText">قوة كلمة المرور</small>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    أوافق على <a href="#" class="text-primary">الشروط والأحكام</a>
                                </label>
                            </div>

                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="bi bi-person-plus me-2"></i>
                                إنشاء الحساب
                            </button>
                        </form>

                        <hr class="my-4">

                        <div class="text-center">
                            <p class="mb-2">لديك حساب بالفعل؟</p>
                            <a href="index.html" class="btn btn-outline-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                تسجيل الدخول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="app-footer">
        <div class="container text-center">
            <p class="mb-1">
                <strong>SallamChat</strong> - للتواصل الحر والسلمي
            </p>
            <p class="mb-0">
                تصميم: م. هاني سلام - جميع الحقوق محفوظة © <span id="currentYear"></span>
            </p>
        </div>
    </footer>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    </div>

    <!-- Alert Container -->
    <div class="alert-container" id="alertContainer"></div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Supabase SDK (ONLY Database) -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <!-- Custom JS -->
    <script src="assets/js/supabase-config.js"></script>
    <script src="assets/js/database-config.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>
